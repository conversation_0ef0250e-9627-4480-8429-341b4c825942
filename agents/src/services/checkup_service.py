from typing import Dict, List, Any
import time
import uuid
from .user_service import UserService

class CheckupService:
    """
    Service for managing automated checkups
    """
    @classmethod
    def schedule_checkup(cls, user_id: str, minutes_from_now: int, message: str) -> Dict[str, Any]:
        """
        Schedule a checkup message
        """
        user_state = UserService.get_user_state(user_id)

        # Create a new checkup
        checkup_id = str(uuid.uuid4())
        checkup = {
            "id": checkup_id,
            "text": message,
            "scheduled_in_minutes": minutes_from_now,
            "created_at": time.time(),
            "scheduled_time": time.time() + (minutes_from_now * 60),
            "completed": False
        }

        # Add to user's checkups
        if "checkups" not in user_state:
            user_state["checkups"] = []
        user_state["checkups"].append(checkup)

        return {
            "checkup_id": checkup_id,
            "scheduled_time": checkup["scheduled_time"]
        }

    @classmethod
    def get_checkups(cls, user_id: str, include_completed: bool = False) -> List[Dict[str, Any]]:
        """
        Get all scheduled checkups for a user
        """
        user_state = UserService.get_user_state(user_id)
        checkups = user_state.get("checkups", [])

        if not include_completed:
            checkups = [c for c in checkups if not c.get("completed", False)]

        return checkups

    @classmethod
    async def process_checkups(cls, user_id: str) -> None:
        """
        Process any checkups that are due
        """
        user_state = UserService.get_user_state(user_id)
        checkups = user_state.get("checkups", [])
        current_time = time.time()

        for checkup in checkups:
            if not checkup.get("completed", False) and current_time >= checkup.get("scheduled_time", 0):
                # Mark as completed
                checkup["completed"] = True
                checkup["completed_at"] = current_time

                # In a real system, this would send a notification to the user
                # For now, we just mark it as completed
                pass

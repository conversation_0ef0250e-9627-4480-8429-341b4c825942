"""
Dynamic Prompt Builder for RAG-based context construction.

This module implements the DynamicPromptBuilder class that constructs prompts
with a three-tier context structure:
1. 5 most recent conversation turns (always included)
2. 5 most recent summaries chronologically (always included)
3. 3 most relevant summaries from older history (semantic search with relevancy threshold)
"""

from typing import Dict, List, Any, Optional


class DynamicPromptBuilder:
    """
    Builds dynamic prompts with a structured three-tier context approach.

    The context structure follows a specific pattern:
    1. Base prompt (system instructions)
    2. User profile
    3. 5 most recent conversation turns
    4. 5 most recent summaries (chronologically)
    5. Up to 3 semantically relevant summaries from older history
    """

    def __init__(self, longterm_store):
        """
        Initialize the dynamic prompt builder.

        Args:
            longterm_store: The long-term vector store for retrieving summaries
        """
        self.longterm_store = longterm_store

    def build_prompt(self,
                    user_input: str,
                    base_prompt: str,
                    user_profile: Optional[Dict] = None,
                    recent_turns: Optional[str] = None,
                    progress_data: Optional[Dict] = None,
                    domain: Optional[str] = None) -> str:
        """
        Build a dynamic prompt by retrieving relevant conversation history based on the user's input.

        Args:
            user_input: The user's input message
            base_prompt: The base system prompt
            user_profile: The user's profile data
            recent_turns: The 5 most recent conversation turns
            progress_data: User progress data (missed meals, unplanned foods, etc.)
            domain: The domain of the agent (nutrition, social_event, general)

        Returns:
            The final prompt with structured context
        """
        user_id = self.longterm_store.user_id or "default_user"

        # Start with the base prompt
        final_prompt = base_prompt

        # Add user profile if available
        if user_profile:
            profile_text = "\n".join([f"{k}: {v}" for k, v in user_profile.items()])
            final_prompt += f"\n\nUSER PROFILE:\n{profile_text}"

        # Add the 5 most recent conversation turns
        if recent_turns:
            final_prompt += f"\n\nRECENT CONVERSATION (Last 5 Turns):\n{recent_turns}"

        # Get the 5 most recent summaries chronologically
        recent_summaries = self.get_recent_summaries(n=5)
        if recent_summaries:
            summaries_text = "\n\nRECENT CONVERSATION SUMMARIES:\n"
            for summary in recent_summaries:
                summaries_text += f"- {summary['content']}\n"
            final_prompt += summaries_text

        # Retrieve semantically relevant summaries (excluding the 5 most recent ones)
        relevant_summaries = self.get_relevant_summaries(user_input, top_k=3, exclude_recent=5)

        if relevant_summaries:
            relevant_text = "\n\nRELEVANT HISTORICAL CONTEXT:\n"
            for summary in relevant_summaries:
                relevant_text += f"- {summary['content']}\n"

            # Only add if we have relevant summaries
            if relevant_text != "\n\nRELEVANT HISTORICAL CONTEXT:\n":
                final_prompt += relevant_text

        # Add progress data if available
        if progress_data:
            progress_text = f"Missed Meals: {progress_data.get('missed_meals_count', 0)}\n"
            progress_text += f"Unplanned Foods: {progress_data.get('unplanned_food_count', 0)}\n"
            progress_text += f"Social Events: {progress_data.get('social_events_count', 0)}"
            final_prompt += f"\n\nPROGRESS DATA:\n{progress_text}"

        # Add final instruction
        final_prompt += "\n\nNow handle the user's message below.\n"

        return final_prompt

    def get_recent_summaries(self, n: int = 5) -> List[Dict[str, Any]]:
        """
        Get the n most recent summaries chronologically.

        Args:
            n: Number of recent summaries to retrieve

        Returns:
            List of the n most recent summaries
        """
        user_id = self.longterm_store.user_id or "default_user"

        # Query for summaries, sorted by timestamp in descending order
        results = self.longterm_store.query_by_metadata(
            filter_dict={"role": {"$eq": "summary"}},
            sort_by="timestamp",
            sort_order="desc",
            limit=n
        )

        return results

    def get_relevant_summaries(self, query_text: str, top_k: int = 3, exclude_recent: int = 5) -> List[Dict[str, Any]]:
        """
        Get the most semantically relevant summaries for the query text.
        Excludes the most recent 'exclude_recent' summaries.

        Args:
            query_text: The text to search for
            top_k: Maximum number of results to return
            exclude_recent: Number of recent summaries to exclude

        Returns:
            List of relevant summaries
        """
        user_id = self.longterm_store.user_id or "default_user"

        # Get all summaries
        all_summaries = self.longterm_store.query_by_metadata(
            filter_dict={"role": {"$eq": "summary"}},
            sort_by="timestamp",
            sort_order="desc"
        )

        # Exclude the most recent ones
        older_summaries = all_summaries[exclude_recent:] if len(all_summaries) > exclude_recent else []

        if not older_summaries:
            return []

        # Perform semantic search on the older summaries
        results = self.longterm_store.query(
            query_text=query_text,
            top_k=top_k,
            filter_dict={
                "role": {"$eq": "summary"},
                "timestamp": {"$lt": older_summaries[0]["metadata"]["timestamp"]}
            },
            relevancy_threshold=0.7  # Apply relevancy threshold
        )

        return results

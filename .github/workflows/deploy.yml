name: Deploy <PERSON><PERSON> AI to AWS

on:
  push:
    branches: [main, production]
  workflow_dispatch:

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com
  ECS_CLUSTER: raeda-cluster

jobs:
  build-and-deploy-api:
    name: Build and Deploy API
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build, tag, and push API image to Amazon ECR
      id: build-api-image
      env:
        ECR_REPOSITORY: raeda-api
        IMAGE_TAG: ${{ github.sha }}
      run: |
        cd api
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

    - name: Update API task definition
      id: api-task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: deployment/api-task-definition.json
        container-name: raeda-api
        image: ${{ steps.build-api-image.outputs.image }}

    - name: Deploy API to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.api-task-def.outputs.task-definition }}
        service: raeda-api-service
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

  build-and-deploy-agents:
    name: Build and Deploy Agents
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build, tag, and push Agents image to Amazon ECR
      id: build-agents-image
      env:
        ECR_REPOSITORY: raeda-agents
        IMAGE_TAG: ${{ github.sha }}
      run: |
        cd agents
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" >> $GITHUB_OUTPUT

    - name: Update Agents task definition
      id: agents-task-def
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: deployment/agents-task-definition.json
        container-name: raeda-agents
        image: ${{ steps.build-agents-image.outputs.image }}

    - name: Deploy Agents to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.agents-task-def.outputs.task-definition }}
        service: raeda-agents-service
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

  deploy-frontend:
    name: Deploy Frontend to Amplify
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Deploy to Amplify
      run: |
        # Amplify automatically deploys when connected to Git repository
        # This step can trigger a manual deployment if needed
        aws amplify start-job \
          --app-id ${{ secrets.AMPLIFY_APP_ID }} \
          --branch-name main \
          --job-type RELEASE

  notify-deployment:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [build-and-deploy-api, build-and-deploy-agents, deploy-frontend]
    if: always()
    
    steps:
    - name: Notify Success
      if: needs.build-and-deploy-api.result == 'success' && needs.build-and-deploy-agents.result == 'success'
      run: |
        echo "✅ Deployment successful!"
        echo "API: Deployed to ECS"
        echo "Agents: Deployed to ECS"
        echo "Frontend: Deployed to Amplify"

    - name: Notify Failure
      if: needs.build-and-deploy-api.result == 'failure' || needs.build-and-deploy-agents.result == 'failure'
      run: |
        echo "❌ Deployment failed!"
        echo "Check the logs for more details."
        exit 1

# Interface Organization Guide

## 🎯 Single Source of Truth Principle

**ALL interfaces should be defined in the `types/` folder, NOT in API files.**

## ✅ Correct Structure

```
mobile/src/
├── types/                    # 📁 SINGLE SOURCE OF TRUTH for all interfaces
│   ├── auth/                # Authentication types
│   ├── api/                 # API request/response types
│   ├── common/              # Shared types
│   ├── components/          # Component-specific types (NEW!)
│   │   ├── overview.types.ts # Overview component interfaces
│   │   └── index.ts         # Component types export
│   ├── food/                # Food domain types
│   └── index.ts             # Main export
├── api/                     # 🔧 API functions ONLY (no interfaces)
│   ├── auth.ts              # Login/register functions
│   ├── meals.api.ts         # Meal API functions
│   └── user.ts              # User API functions
└── screens/                 # 📱 React components
    ├── LoginScreen.tsx
    └── RegisterScreen.tsx
```

## 🎯 Component Types Organization

### When to use `types/components/`
- **Component Props**: Interfaces for component props (e.g., `OverviewProps`)
- **Component State**: Local state interfaces (e.g., `OverviewState`)
- **UI-specific types**: Types that are specific to UI components and not business logic
- **Consolidated interfaces**: When the same component type is used across multiple files

### Example: Overview Component Types
```typescript
// types/components/overview.types.ts
export interface UserOverview {
  userName: string;
  goal: string;
  achievements?: string[];
  // ... other properties
}

export interface OverviewProps {
  engagementRate: number;
  userOverview: UserOverview;
}
```

```typescript
// components/tabs/Overview.native.tsx
import { OverviewProps } from '../../types';

export const Overview: React.FC<OverviewProps> = ({ engagementRate, userOverview }) => {
  // Component implementation
};
```

## 📝 Import Patterns

### ✅ CORRECT - Import types from types folder
```typescript
// In screens/LoginScreen.tsx
import { loginApi } from '../api/auth';           // API function
import type { LoginData } from '../types/auth';   // Type definition

// In api/auth.ts
import type { LoginData } from '../types/auth';   // Import type
export const loginApi = {
  login: async (data: LoginData) => { ... }       // Use type
};
```

### ❌ WRONG - Don't define interfaces in API files
```typescript
// DON'T DO THIS in api/auth.ts
export interface LoginData {  // ❌ Wrong place
  email: string;
  password: string;
}
```

## 🔄 Migration Steps

1. **Move all interfaces** from `api/` files to appropriate `types/` folders
2. **Update API files** to import types from `types/` folder
3. **Update screens** to import types from `types/` folder
4. **Remove duplicate interfaces** from API files

## 🎯 Benefits

- ✅ **Single source of truth** - No duplicate interfaces
- ✅ **Better organization** - Types grouped by domain
- ✅ **Easier maintenance** - Update types in one place
- ✅ **Better imports** - Clear separation of concerns
- ✅ **Type safety** - Consistent type definitions across app

## 📋 Checklist

- [x] All interfaces moved to `types/` folder
- [x] API files only contain functions, not interfaces
- [x] Screens import types from `types/` folder
- [x] No duplicate interface definitions
- [x] All imports use `type` keyword for type-only imports

## ✅ Completed Migration

The interface organization has been successfully completed:

### Fixed Issues:
1. **Forget Password Types**: Moved `ForgotPasswordData`, `VerifyOtpData`, and `ResetPasswordData` from `forget-password.api.ts` to `types/auth/forget-password.types.ts`
2. **Meal Types**: Moved `MealType` interface from `meals.api.ts` to `types/meal.ts`
3. **UserWorkoutPlan Component Types**: Moved `UserWorkoutPlanProps` and `ExpandedExerciseStatsProps` from component file to `types/components/workout-plan.types.ts`
4. **Workout Domain Types**: Enhanced `Exercise` interface in `types/workout.ts` to include `videoUrl` property
5. **Updated Exports**: All type exports are properly configured in index files
6. **Import Statements**: All API files and components now import types using `type` keyword from types folder

### Current Structure:
```
mobile/src/
├── types/
│   ├── auth/
│   │   ├── forget-password.types.ts  ✅ NEW
│   │   ├── login.types.ts
│   │   ├── register.types.ts
│   │   ├── user.types.ts
│   │   └── index.ts                  ✅ UPDATED
│   ├── components/
│   │   ├── overview.types.ts
│   │   ├── workout-plan.types.ts     ✅ NEW
│   │   └── index.ts                  ✅ UPDATED
│   ├── workout.ts                    ✅ UPDATED (added videoUrl)
│   ├── meal.ts                       ✅ UPDATED
│   └── index.ts                      ✅ UPDATED
├── api/                              ✅ ALL CLEAN
│   ├── auth.ts                       ✅ Imports from types
│   ├── forget-password.api.ts        ✅ Imports from types
│   ├── meals.api.ts                  ✅ Imports from types
│   ├── training.api.ts               ✅ No interfaces
│   ├── user.ts                       ✅ Imports from types
│   ├── workout-logs.api.ts           ✅ Imports from types
│   └── workout-sets.api.ts           ✅ Imports from types
├── components/
│   ├── users/
│   │   └── UserWorkoutPlan.tsx       ✅ CLEAN (imports from types)
│   └── ...
```

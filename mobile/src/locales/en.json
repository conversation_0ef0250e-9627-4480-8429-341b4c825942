{"App": {"name": "Raeda-AI"}, "login": {"title": "Login to your account", "email": "Email address", "password": "Password", "forgotPassword": "Forgot Password?", "signIn": "Sign In", "signUp": "Sign up", "needAccount": "Need an account?", "validation": {"emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Password is required", "passwordLength": "Password must be at least 8 characters long", "passwordUppercase": "Password must contain at least one uppercase letter", "passwordLowercase": "Password must contain at least one lowercase letter", "passwordNumber": "Password must contain at least one number", "passwordSpecialChar": "Password must contain at least one special character (!@#$%^&*)"}, "errors": {"invalidEmail": "Please enter a valid email address.", "emailRequired": "Email is required.", "passwordRequired": "Password is required.", "invalidPasswordPattern": "Password must contain at least 8 characters, including uppercase, lowercase, number, and special character.", "invalidCredentials": "Invalid email or password.", "userNotFound": "User not found.", "roleNotFound": "User role not found.", "serverConfiguration": "Server configuration error. Please try again later.", "saveLoginDataFailed": "Failed to save login data. Please try again.", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "unexpectedError": "An unexpected error occurred. Please try again.", "OnlyTraineesAllowed": "Only trainee users can login from mobile app"}}, "forgotPassword": {"title": "Raeda-AI", "subtitle": "Forgot Password", "emailPlaceholder": "Enter your email address", "sendOTP": "Send OTP", "sending": "Sending...", "backToLogin": "Back to Login", "errors": {"emailRequired": "Email is required.", "invalidEmail": "Please enter a valid email address.", "accountNotFound": "No account found with this email address.", "otpSentIfExists": "If an account with this email exists, a password reset OTP has been sent.", "emailConfiguration": "Email configuration error. Please try again later.", "failedRequest": "Failed to process password reset request. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again."}}, "resetPassword": {"title": "Raeda-AI", "subtitle": "Reset Your Password", "otpPlaceholder": "Enter OTP from your email", "newPasswordPlaceholder": "New Password", "confirmPasswordPlaceholder": "Confirm New Password", "resetButton": "Reset Password", "resendButton": "Resend OTP", "resendCountdown": "Resend OTP in {{seconds}}s", "errors": {"emailRequired": "Email is required.", "invalidEmail": "Please enter a valid email address.", "otpRequired": "OTP code is required.", "passwordRequired": "New password is required.", "passwordLength": "Password must be at least 8 characters long.", "invalidOtp": "The OTP code you entered is invalid.", "otpExpired": "The OTP code has expired. Please request a new one.", "userNotFound": "User not found.", "firebaseUpdateFailed": "Failed to update password. Please try again.", "unexpectedError": "An unexpected error occurred. Please try again."}}, "userScreen": {"tabs": {"overview": "Overview", "mealPlan": "Meal Plan", "workoutPlan": "Training Plan", "workoutLogger": "Workout Log"}, "metrics": {"weight": "Weight", "bodyFat": "Body Fat", "score": "Score", "kg": "kg", "decreased": "Decreased", "increased": "Increased", "noChange": "No Change", "excellent": "🔥 Excellent!", "good": "👍 Good", "onTrack": "💪 On Track"}, "updates": {"title": "Latest Updates", "latestAchievement": "Latest Achievement", "latestActivity": "Latest Activity", "workoutAdjusted": "Workout plan adjusted for recovery needs", "completedAssessment": "Completed physiotherapy assessment"}, "membership": {"timeLeft": "{{days}} days"}, "actions": {"logout": "Logout", "continueWorkout": "Continue", "restComplete": "Rest Time Completed!"}}, "mealPlan": {"loading": "Loading meal plan...", "retry": "Tap to retry", "empty": "No meal plan found. Wait for trainer update.", "dailyMealPlan": "Daily Meal Plan", "totalDailyMacros": "Total Daily Macros", "proteinLabel": "<PERSON>teins", "carbLabel": "<PERSON><PERSON>", "otherLabel": "Other", "mealMacroRange": "Meal Macro Range", "serving": "{{amount}}g", "protein": "{{value}} protein", "carbs": "{{value}} carbs", "fats": "{{value}} fats", "calories": "{{value}} calories", "noMoreMealPlan": "No meal plan found. Wait for trainer update.", "foodItems": "food items", "error": "Failed to load meal plan"}, "units": {"grams": "{{value}}g", "min": "min"}, "workoutInfo": {"title": "Workout Controls", "pauseTimer": "Pause Workout - Stop the rest timer and allow pause", "continueWorkout": "Continue Workout - Resume workout after pause", "skipExercise": "Skip Exercise - Move to the next exercise", "skipRest": "Skip Rest - Complete the current rest time", "endWorkout": "End Workout - Complete the current workout"}, "workoutPlan": {"loading": "Loading training plan...", "invalidVideo": "Invalid YouTube URL", "title": "Weekly training plan", "subtitle": "Your personal plan", "tip": {"label": "Tip:", "details": "Click on the exercise name to see more details and instructions.", "collapsedExercise": "Click on an exercise name to see more details.", "collapsedDay": "Click on training day to see exercises."}, "noTrainingDays": "There are no training days in the program at the moment.", "exercises": "{{count}} exercises", "dayTitle": "{{day}} - {{focus}} Day", "watchDemo": "Watch the demo", "instructionsHeader": "Instructions for implementation:", "rest": "Rest", "sets": "Sets", "reps": "Reps"}, "workoutLogger": {"title": "Workout Log", "loading": "Loading workout data...", "selectDayLabel": "Select Workout Day", "chooseDayOption": "Choose <PERSON>", "startWorkout": "Start Workout", "exerciseName": "Exercise", "weight": "Weight (kg)", "weightPlaceholder": "Enter weight", "reps": "Reps (Target: {{target}})", "repsPlaceholder": "Enter reps", "completeSet": "Complete Set", "setsRemaining": "Sets Remaining", "exercisesRemaining": "Exercises Remaining", "workoutDuration": "Workout Duration", "nextExercise": "Next Exercise", "restTimer": "Rest: {{time}}", "skipRest": "<PERSON><PERSON>", "finishWorkout": "Finish Workout", "finishConfirmation": "Finish Workout?", "finishWarning": "Progress cannot be recovered.", "cancel": "Cancel", "confirm": "Finish Workout", "workoutComplete": "Your workout is complete. Click 'Finish Workout' to close the workout session.", "note": "Note", "controlsHelp": {"title": "Control Buttons:", "pause": "- Pause Workout: Stops rest timer", "continue": "- Continue Workout: Resumes workout", "skipExercise": "- Skip Exercise: Move to next exercise", "skipRest": "- Skip Rest: Ends current rest period", "finish": "- Finish Workout: Completes workout"}, "errors": {"noWorkout": "Could not restore your workout. You may need to restart it.", "restoreWorkoutError": "Could not restore your workout. You may need to restart it.", "startWorkoutError": "Unable to start workout due to an unexpected error.", "onlyTrainees": "Only trainees can log workouts.", "alreadyCompleted": "You've already completed a workout today.", "noAccessToPlan": "You do not have access to this training plan.", "unexpectedError": "An unexpected error occurred. Please try again."}, "restTimeCompleted": {"title": "Rest Time Completed!", "message": "Return to workout to continue"}, "sessionEnd": {"title": "Please End Workout Session!", "message": "Return to workout to end the session."}, "minutes": "{{value}} min", "continue": "Continue", "restTime": "Rest Time", "paused": "Paused"}, "errors": {"emailRequired": "Email is required", "passwordRequired": "Password is required", "loginFailed": "<PERSON><PERSON> failed. Please check your credentials.", "saveLoginFailed": "Failed to save login data. Please try again.", "emailEmpty": "Please enter your email address", "invalidEmail": "Please enter a valid email address", "resetFailed": "Failed to send reset instructions. Please try again.", "noResponse": "No response from server. Please check your internet connection.", "unexpectedError": "An unexpected error occurred. Please try again.", "passwordMismatch": "Passwords don't match", "passwordTooShort": "Password must be at least 8 characters long", "resetPasswordFailed": "Failed to reset password. Please try again.", "resendOtpFailed": "Failed to resend OTP. Please try again.", "failedToLoadUserMealPlan": "Failed to load user meal plan", "failedToLoadTrainingPlan": "Failed to load training plan"}, "success": {"resetInstructions": "Password reset instructions sent to your email.", "passwordReset": "Password successfully reset!", "otpResent": "OTP resent successfully!"}, "common": {"tapToRetry": "הקש כדי לנסות שוב"}}
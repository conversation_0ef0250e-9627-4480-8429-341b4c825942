{"App": {"name": "ראדה-AI"}, "login": {"title": "התחבר לחשבונך", "email": "כתובת אימייל", "password": "סיסמה", "forgotPassword": "שכחת סיסמה?", "signIn": "התח<PERSON>ר", "signUp": "הרשמה", "needAccount": "צריך חשבון?", "validation": {"emailRequired": "נדרש להזין אימייל", "emailInvalid": "אנא הזן כתובת אימייל תקינה", "passwordRequired": "נדרש להזין סיסמה", "passwordLength": "הסיסמה חייבת להיות באורך של לפחות 8 תווים", "passwordUppercase": "הסיסמה חייבת להכיל לפחות אות גדולה אחת", "passwordLowercase": "הסיסמה חייבת להכיל לפחות אות קטנה אחת", "passwordNumber": "הסיסמה חייבת להכיל לפחות מספר אחד", "passwordSpecialChar": "הסיסמה חייבת להכיל לפחות תו מיוחד אחד (!@#$%^&*)"}, "errors": {"invalidEmail": "אנא הזן כתובת אימייל תקינה.", "emailRequired": "נדרש להזין אימייל.", "passwordRequired": "נדרש להזין סיסמה.", "invalidPasswordPattern": "הסיסמה חייבת להכיל לפחות 8 תווים, כולל אות גדולה, אות קטנה, מספר ותו מיוחד.", "invalidCredentials": "אימייל או סיסמה לא נכונים.", "userNotFound": "המשתמש לא נמצא.", "roleNotFound": "תפ<PERSON>יד המשתמש לא נמצא.", "serverConfiguration": "שגיאת הגדרות שרת. נסה שוב מאוחר יותר.", "saveLoginDataFailed": "שמירת נתוני ההתחברות נכשלה. נסה שוב.", "loginFailed": "ההתחברות נכשלה. בדוק את הפרטים ונסה שוב.", "unexpectedError": "אירעה שגיאה בלתי צפויה. נסה שוב.", "OnlyTraineesAllowed": "רק משתמשים מסוג מתאמן יכולים להתחבר דרך האפליקציה הניידת"}}, "forgotPassword": {"title": "שכ<PERSON><PERSON> סיסמה", "subtitle": "שחזור סיסמה", "emailPlaceholder": "הזן את כתובת האימייל שלך", "sendOTP": "שלח קוד אימות", "sending": "שולח...", "backToLogin": "חזרה להתחברות", "errors": {"emailRequired": "נדרש להזין אימייל.", "invalidEmail": "אנא הזן כתובת אימייל תקינה.", "accountNotFound": "לא נמצא חשבון עם כתובת אימייל זו.", "otpSentIfExists": "אם קיים חשבון עם כתובת אימייל זו, קוד לאיפוס סיסמה נשלח.", "emailConfiguration": "שגיאת הגדרות דוא\"ל. נסה שוב מאוחר יותר.", "failedRequest": "לא ניתן לעבד את בקשת איפוס הסיסמה. נסה שוב.", "unexpectedError": "אירעה שגיאה בלתי צפויה. נסה שוב."}}, "resetPassword": {"title": "ראדה-AI", "subtitle": "איפוס הסיסמה שלך", "otpPlaceholder": "הזן קוד אימות מהאימייל שלך", "newPasswordPlaceholder": "סיסמה חדשה", "confirmPasswordPlaceholder": "אימות סיסמה חדשה", "resetButton": "איפוס סיסמה", "resendButton": "שלח קוד אימות מחדש", "resendCountdown": "שלח קוד אימות מחדש בעוד {{seconds}}s", "errors": {"emailRequired": "נדרש להזין אימייל.", "invalidEmail": "אנא הזן כתובת אימייל תקינה.", "otpRequired": "נדרש להזין קוד אימות.", "passwordRequired": "נדרש להזין סיסמה חדשה.", "passwordLength": "הסיסמה חייבת להיות באורך של לפחות 8 תווים.", "invalidOtp": "קוד האימות שהוזן אינו תקין.", "otpExpired": "קוד האימות פג תוקף. אנא בקש קוד חדש.", "userNotFound": "המשתמש לא נמצא.", "firebaseUpdateFailed": "נכשל עדכון הסיסמה. נסה שוב.", "unexpectedError": "אירעה שגיאה בלתי צפויה. נסה שוב."}}, "userScreen": {"tabs": {"overview": "סקירה כללית", "mealPlan": "תוכנית תזונה", "workoutPlan": "תוכנית אימונים", "workoutLogger": "יו<PERSON><PERSON> אימונים"}, "metrics": {"weight": "משקל", "bodyFat": "שומן גוף", "score": "ציון", "kg": "ק\"ג", "decreased": "ירד", "increased": "עלה", "noChange": "ללא שינוי", "excellent": "🔥 מצוין!", "good": "👍 טוב", "onTrack": "💪 בדרך"}, "updates": {"title": "עדכונים אחרונים", "latestAchievement": "הי<PERSON><PERSON> אחרון", "latestActivity": "פעילות אחרונה", "workoutAdjusted": "תוכנית האימונים הותאמה לצרכי ההתאוששות", "completedAssessment": "השלים הערכת פיזיותרפיה"}, "membership": {"timeLeft": "{{days}} ימים"}, "actions": {"logout": "התנתק", "continueWorkout": "המשך אימון", "restComplete": "זמן מנוחה הסתיים!"}}, "mealPlan": {"loading": "טוען תוכנית תזונה...", "retry": "הקש כדי לנסות שוב", "empty": "לא נמצאה תוכנית תזונה. המתן לעדכון מהמאמן.", "dailyMealPlan": "תוכנית תזונה יומית", "totalDailyMacros": "סה\"כ מאקרו יומי", "proteinLabel": "חלבונים", "carbLabel": "פחמימות", "otherLabel": "<PERSON><PERSON><PERSON>", "mealMacroRange": "טווח מאקרו לארוחה", "serving": "{{amount}} ג", "protein": "{{value}} חל<PERSON><PERSON>ן", "carbs": "{{value}} פחמימות", "fats": "{{value}} שומנים", "calories": "{{value}} קלוריות", "foodItems": "פריטי מזון", "error": "טעינת תוכנית התזונה נכשלה", "noMoreMealPlan": "לא נמצאה תוכנית ארוחות. אנא המתן לעדכון מהמאמן."}, "workoutPlan": {"loading": "טוען תוכנית אימונים...", "invalidVideo": "כתובת יוטיוב לא תקינה", "title": "תוכנית אימונים שבועית", "subtitle": "התוכנית האישית שלך", "tip": {"label": "טיפ:", "details": "לחץ על שם התרגיל כדי לראות פרטים נוספים והוראות.", "collapsedExercise": "לחץ על שם תרגיל כדי לראות פרטים נוספים.", "collapsedDay": "לחץ על יום אימון כדי לראות תרגילים."}, "noTrainingDays": "אין כרגע ימי אימון בתוכנית.", "exercises": "{{count}} תרגילים", "dayTitle": "{{day}} - {{focus}} יום", "watchDemo": "צ<PERSON>ה בדמו", "instructionsHeader": "הוראות לביצוע:", "rest": "מנוחה", "sets": "סטים", "reps": "חזרות"}, "units": {"grams": "{{value}}ג", "min": "ד<PERSON>'"}, "workoutInfo": {"title": "לפתוחי שליחה", "pauseTimer": "שעור אימון - עצור את טיימר המנוחה ומאפשר הפסקה", "continueWorkout": "המשך אימון - המשיך את האימון לאחר השהייה", "skipExercise": "דלג על הרגיל - עבור להרגיל הבא", "skipRest": "דלג על מנוחה - מיים את זמן המנוחה הנוכחי", "endWorkout": "סיום אימון - משלים את האימון הנוכחי"}, "workoutLogger": {"title": "יו<PERSON><PERSON> אימונים", "loading": "טוען נתוני אימון...", "selectDayLabel": "ב<PERSON>ר יום אימון", "chooseDayOption": "<PERSON><PERSON><PERSON> יום", "startWorkout": "התחל אימון", "exerciseName": "תרגיל", "weight": "משקל (ק\"ג)", "weightPlaceholder": "<PERSON><PERSON><PERSON> משקל", "reps": "חזרות (יעד: {{target}})", "repsPlaceholder": "הזן חזרות", "completeSet": "סיים סט", "setsRemaining": "סטים נותרו", "exercisesRemaining": "תרגילים נותרו", "workoutDuration": "משך האימון", "nextExercise": "תרגיל הבא", "restTimer": "מנוחה: {{time}}", "skipRest": "דלג", "finishWorkout": "סיים אימון", "finishConfirmation": "לסיים את האימון?", "finishWarning": "לא ניתן לשחזר את ההתקדמות.", "cancel": "ביטול", "confirm": "סיים אימון", "workoutComplete": "האימון שלך הסתיים. לחץ על 'סיים אימון' כדי לסגור את מפגש האימון.", "note": "הערה", "controlsHelp": {"title": "כפתורי שליטה:", "pause": "- הש<PERSON>ה אימון: עוצר את טיימר המנוחה", "continue": "- המשך אימון: ממשיך את האימון", "skipExercise": "- דלג על תרגיל: עבור לתרגיל הבא", "skipRest": "- דלג על מנוחה: מסיים את זמן המנוחה הנוכחי", "finish": "- סיים אימון: משלים את האימון"}, "errors": {"noWorkout": "לא ניתן לשחזר את האימון שלך. ייתכן שתצטרך להתחיל מחדש.", "startWorkoutError": "לא ניתן להתחיל את האימון עקב שגיאה בלתי צפויה.", "restoreWorkoutError": "לא ניתן לשחזר את האימון שלך. ייתכן שתצטרך להתחיל מחדש.", "onlyTrainees": "רק מתאמנים יכולים לרשום אימונים.", "alreadyCompleted": "כ<PERSON>ר השלמת אימון היום.", "noAccessToPlan": "אין לך גישה לתוכנית אימונים זו.", "unexpectedError": "אירעה שגיאה בלתי צפויה. נסה שוב."}, "restTimeCompleted": {"title": "<PERSON><PERSON><PERSON> המנוחה הסתיים!", "message": "חזרו לאימון כדי להמשיך"}, "sessionEnd": {"title": "אנא סיימו את האימון!", "message": "חזרו לאימון כדי לסיים את המפגש."}, "minutes": "{{value}} דק<PERSON>ת", "continue": "המשך", "restTime": "<PERSON><PERSON><PERSON> מנוחה", "paused": "מושהה"}, "errors": {"emailRequired": "יש להזין כתובת אימייל", "passwordRequired": "יש להזין סיסמה", "loginFailed": "ההתחברות נכשלה. אנא בדוק את הפרטים שלך.", "saveLoginFailed": "שמירת נתוני ההתחברות נכשלה. נסה שוב.", "emailEmpty": "אנא הזן את כתובת האימייל שלך", "invalidEmail": "אנא הזן כתובת אימייל תקינה", "resetFailed": "שליחת הוראות איפוס הסיסמה נכשלה. אנא נסה שוב.", "noResponse": "אין תגובה מהשרת. אנא בדוק את חיבור האינטרנט שלך.", "unexpectedError": "אירעה שגיאה בלתי צפויה. אנא נסה שוב.", "passwordMismatch": "הסיסמאות אינן תואמות", "passwordTooShort": "הסיסמה חייבת להכיל לפחות 8 תווים", "resetPasswordFailed": "איפוס הסיסמה נכשל. אנא נסה שוב.", "resendOtpFailed": "שליחת קוד האימות מחדש נכשלה. אנא נסה שוב.", "failedToLoadUserMealPlan": "נכשל בטעינת תוכנית הארוחות של המשתמש", "failedToLoadTrainingPlan": "נכ<PERSON>ל בטעינת תוכנית האימונים"}, "success": {"resetInstructions": "הוראות לאיפוס סיסמה נשלחו לאימייל שלך.", "passwordReset": "הסיסמה אופסה בהצלחה!", "otpResent": "קוד האימות נשלח מחדש בהצלחה!"}}
// Common shared types used across the application

export interface BaseEntity {
  id: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface SelectOption {
  label: string;
  value: string | number;
  disabled?: boolean;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

export interface FormState<T> extends LoadingState {
  data: T;
  isDirty: boolean;
  isValid: boolean;
  errors: Record<keyof T, string>;
}

export type ThemeMode = 'light' | 'dark';

// Navigation types
export interface NavigationState {
  currentRoute: string;
  previousRoute?: string;
}

// Device types
export type DevicePlatform = 'ios' | 'android' | 'web';
export type DeviceOrientation = 'portrait' | 'landscape';

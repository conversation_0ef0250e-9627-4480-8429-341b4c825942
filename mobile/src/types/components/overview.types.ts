// Overview component types - consolidated from multiple sources

/**
 * User overview data structure
 * Consolidated from mobile/src/data/overview.ts and component interfaces
 */
export interface UserOverview {
  userId?: string;
  userName: string;
  email?: string;
  goal: string;
  avatarUrl?: string | null;
  profileImage?: any; // For backward compatibility
  
  // Physical stats
  stats?: {
    weight: number;
    height: number;
    age: number;
  };
  startingWeight?: number;
  currentWeight?: number;
  startingFatPercentage?: number;
  currentFatPercentage?: number;
  
  // Progress tracking
  progressOverview?: string;
  progressLevel?: number;
  weeklyIntensity?: number;
  
  // Engagement metrics
  engagementLevel?: number;
  engagement?: {
    lastLogin: string;
    sessionsThisWeek: number;
    score: number;
  };
  
  // Additional data
  achievements?: string[];
  latestChanges?: string;
  latestEvents?: string;
  membershipEndDate?: string;
  
  // Progress tracking
  progress?: {
    rate: number;
  };
}

/**
 * Props for the Overview component
 */
export interface OverviewProps {
  engagementRate: number;
  userOverview: UserOverview;
  isLoading?: boolean;
  isTrainerView?: boolean;
}

/**
 * Overview component state interface
 */
export interface OverviewState {
  isLoading: boolean;
  error?: string | null;
  refreshing?: boolean;
}

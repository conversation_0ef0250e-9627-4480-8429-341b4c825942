export interface LoginData {
  email: string;
  password?: string;
}

export interface LoginResponse {
  message: string;
  jwtToken: string;
  user: {
    id: string;
    email: string;
    name: string;
    isActive: boolean;
    role: {
      id: number;
      name: string;
    };
  };
}

export interface LoginFormData {
  email: string;
  password: string;
}

export interface LoginValidationErrors {
  email?: string;
  password?: string;
  general?: string;
}

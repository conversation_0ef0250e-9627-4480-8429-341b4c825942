export interface RegisterData {
  name?: string;
  email: string;
  password?: string;
  role: "trainer" | "trainee";
}

export interface RegisterResponse {
  token: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: "trainer" | "trainee";
  };
}

export interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: "trainer" | "trainee";
}

export interface RegisterValidationErrors {
  name?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  general?: string;
}

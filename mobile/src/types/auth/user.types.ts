export interface Role {
  id: number;
  name: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  isActive: boolean;
  role: Role;
}

export interface UserProfile extends User {
  avatar?: string;
  mobileNumber?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  height?: number;
  weight?: number;
  fitnessLevel?: 'beginner' | 'intermediate' | 'advanced';
}

export interface AuthState {
  isLoading: boolean;
  userToken: string | null;
  userId: string | null;
  user: User | null;
  isAuthenticated: boolean;
}

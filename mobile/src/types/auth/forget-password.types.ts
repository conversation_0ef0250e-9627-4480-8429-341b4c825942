// Forget Password related types
export interface ForgotPasswordData {
  email: string;
}

export interface VerifyOtpData {
  email: string;
  otp: string;
}

export interface ResetPasswordData {
  email: string;
  otp: string;
  newPassword: string;
}

// Form data types for UI components
export interface ForgotPasswordFormData {
  email: string;
}

export interface VerifyOtpFormData {
  otp: string;
}

export interface ResetPasswordFormData {
  otp: string;
  newPassword: string;
  confirmPassword: string;
}

// Validation error types
export interface ForgotPasswordValidationErrors {
  email?: string;
  general?: string;
}

export interface VerifyOtpValidationErrors {
  otp?: string;
  general?: string;
}

export interface ResetPasswordValidationErrors {
  otp?: string;
  newPassword?: string;
  confirmPassword?: string;
  general?: string;
}

// Response types
export interface ForgotPasswordResponse {
  message: string;
  success: boolean;
}

export interface VerifyOtpResponse {
  message: string;
  success: boolean;
  isValid: boolean;
}

export interface ResetPasswordResponse {
  message: string;
  success: boolean;
}

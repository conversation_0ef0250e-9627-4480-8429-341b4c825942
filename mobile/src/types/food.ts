export interface MacroNutrients {
  protein: number;
  carbs: number;
  fats: number;
  calories: number;
}

export interface MacroRange {
  min: MacroNutrients;
  max: MacroNutrients;
}

export interface FoodItem {
  id: string;
  name: string;
  category: string;
  defaultServing: number;
  macrosPer100g: MacroNutrients;
}

export interface NutritionInfo {
  macros: MacroNutrients;
  vitamins?: Record<string, number>;
  minerals?: Record<string, number>;
}

export interface FoodCategory {
  id: string;
  name: string;
  description?: string;
  foods: FoodItem[];
}

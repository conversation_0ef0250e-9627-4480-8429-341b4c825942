// Main types export file - single source of truth for all types

// Auth types
export * from './auth';

// API types
export * from './api';

// Common types
export * from './common';

// Component types
export * from './components';

// Domain-specific types
export * from './food';
export * from './meal';
export * from './workout';
export * from './excercise';
export * from './roadmap';

// Theme types
export * from './theme';

// Re-export commonly used types for convenience
export type {
  User,
  LoginData,
  RegisterData,
  ForgotPasswordData,
  VerifyOtpData,
  ResetPasswordData,
  ApiResponse,
  LoadingState,
} from './auth';

export type {
  MacroNutrients,
  FoodItem,
} from './food';

export type {
  WorkoutLog,
  Exercise,
} from './workout';

export type {
  MealType,
} from './meal';

export type {
  UserOverview,
  OverviewProps,
  OverviewState,
  UserWorkoutPlanProps,
  ExpandedExerciseStatsProps,
} from './components';

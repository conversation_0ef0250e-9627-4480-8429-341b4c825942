import http from "../services/http";

function handleAxiosError(err: any): never {
  if (err.isAxiosError && err.response) {
    const msg = err.response.data?.message ?? "An unexpected error occurred";
    throw new Error(msg);
  }
  throw new Error("An error occurred, please try again.");
}

export const UserApi = {
  getUserDetails: async (userId: string): Promise<any> => {
    try {
      const res = await http.get<any>(`trainer-assignments/trainee/${userId}`);
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },
};

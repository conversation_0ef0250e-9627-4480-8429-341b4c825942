import http from "../services/http";
import type { MealType } from "../types/meal";

export const mealsApi = {
  create: async (meal: MealType): Promise<MealType> => {
    try {
      const response = await http.post(`/meals`, meal);
      return response.data;
    } catch (error: any) {
      console.error(
        "Meal creation error:",
        error.response?.data || error.message
      );
      throw error.response?.data || error;
    }
  },

  findByTrainee: async (traineeId: string): Promise<MealType[]> => {
    try {
      const response = await http.get(`/meals/trainee/${traineeId}`);
      return response.data;
    } catch (error: any) {
      console.error("Find meals error:", error.response?.data || error.message);
      throw error.response?.data || error;
    }
  },

  update: async (
    traineeId: string,
    mealData: Partial<MealType>
  ): Promise<MealType[]> => {
    try {
      const response = await http.put(`/meals/trainee/${traineeId}`, mealData);
      return response.data;
    } catch (error: any) {
      console.error(
        "Meal update error:",
        error.response?.data || error.message
      );
      throw error.response?.data || error;
    }
  },

  remove: async (mealId: string): Promise<MealType[]> => {
    try {
      const response = await http.delete(`/meals/${mealId}`);
      return response.data;
    } catch (error: any) {
      console.error(
        "Meal delete error:",
        error.response?.data || error.message
      );
      throw error.response?.data || error;
    }
  },
};

import http from '../services/http';
import type {
  ForgotPasswordData,
  VerifyOtpData,
  ResetPasswordData,
} from '../types/auth';

export const otpApi = {
    forgotPassword: async (data: ForgotPasswordData): Promise<any> => {
        try {
            const response = await http.post(`/otp/forgot-password`, data);
            return response.data;
        } catch (error) {
            console.error(error);
            throw error;
        }
    },

    verifyOtp: async (data: VerifyOtpData): Promise<any> => {
        try {
            const response = await http.post(`/otp/verify-otp`, data);
            return response.data;
        } catch (error) {
            console.error(error);
            throw error;
        }
    },

    resetPassword: async (data: ResetPasswordData): Promise<any> => {
        try {
            const response = await http.post(`/otp/reset-password`, data);
            return response.data;
        } catch (error) {
            console.error(error);
            throw error;
        }
    },
};

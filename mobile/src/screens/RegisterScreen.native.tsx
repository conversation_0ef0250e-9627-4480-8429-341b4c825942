import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Platform,
  KeyboardAvoidingView,
  SafeAreaView,
  Animated
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Mail, Lock, User, LogIn, Eye, EyeOff } from 'lucide-react-native';
import { registerApi } from '../api/auth';
import type { RegisterData } from '../types/auth';
import { useTheme } from '../contexts/ThemeContext';
import { createStyles } from '../style/Register.style';

const RegisterScreen: React.FC = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const navigation = useNavigation();
  const fadeAnim = useState(new Animated.Value(0))[0];
  const { isDark } = useTheme();
  const styles = createStyles(isDark ? 'dark' : 'light');

  useEffect(() => {
    if (error || success) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true
      }).start();

      const timeoutId = setTimeout(() => {
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true
        }).start(() => {
          setError(null);
          if (success) {
            navigation.navigate('Login' as never);
          }
        });
      }, 3000);

      return () => clearTimeout(timeoutId);
    }
  }, [error, success, fadeAnim, navigation]);

  const validateInputs = (): boolean => {
    if (!name.trim()) {
      setError('Please enter your name');
      return false;
    }

    if (!email.trim()) {
      setError('Please enter your email');
      return false;
    }

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }

    if (!password || password.length < 6) {
      setError('Password must be at least 6 characters');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    setError(null);
    setSuccess(null);

    if (!validateInputs()) {
      return;
    }

    setLoading(true);

    try {
      const userData: RegisterData = {
        name,
        email,
        password,
        role: 'trainee',
      };

      await registerApi.register(userData);
      setLoading(false);
      setSuccess("Registration successful! Please log in with your credentials.");
    } catch (error: any) {
      setLoading(false);

      // Handle specific API error messages if available
      if (error.message) {
        setError(error.message);
      } else if (error.email) {
        setError(`Email error: ${error.email}`);
      } else {
        setError('Registration failed. Please try again.');
      }

      console.error("Registration Error:", error);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const navigateToLogin = () => {
    navigation.navigate('Login' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.header}>
            <Text style={styles.title}>Raeda-AI</Text>
            <Text style={styles.subtitle}>Create a new account</Text>
          </View>

          {loading && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color={isDark ? "#E9D5FF" : "#3B82F6"} />
            </View>
          )}

          {error && (
            <Animated.View style={[styles.errorContainer, { opacity: fadeAnim }]}>
              <Text style={styles.errorText}>{error}</Text>
            </Animated.View>
          )}

          {success && (
            <Animated.View style={[styles.successContainer, { opacity: fadeAnim }]}>
              <Text style={styles.successText}>{success}</Text>
            </Animated.View>
          )}

          <View style={styles.inputContainer}>
            {/* Name Input */}
            <View style={styles.inputWrapper}>
              <User size={20} color="#9ca3af" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Full Name"
                value={name}
                onChangeText={setName}
                autoCapitalize="words"
                editable={!loading}
              />
            </View>

            {/* Email Input */}
            <View style={styles.inputWrapper}>
              <Mail size={20} color="#9ca3af" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Email address"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                editable={!loading}
              />
            </View>

            {/* Password Input */}
            <View style={styles.inputWrapper}>
              <Lock size={20} color="#9ca3af" style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoComplete="password"
                editable={!loading}
              />
              <TouchableOpacity onPress={togglePasswordVisibility} style={styles.eyeIcon} disabled={loading}>
                {showPassword ? (
                  <EyeOff size={20} color="#9ca3af" />
                ) : (
                  <Eye size={20} color="#9ca3af" />
                )}
              </TouchableOpacity>
            </View>

            {/* Role Display - Single Trainee Option */}
            <View style={styles.roleDisplay}>
              <Text style={styles.roleLabel}>Role:</Text>
              <View style={styles.roleValueContainer}>
                <Text style={styles.roleValue}>Trainee</Text>
              </View>
            </View>
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            style={[styles.registerButton, loading && styles.disabledButton]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <LogIn size={20} color="#ffffff" />
            <Text style={styles.registerButtonText}>Sign Up</Text>
          </TouchableOpacity>

          <TouchableOpacity onPress={navigateToLogin} style={styles.loginContainer} disabled={loading}>
            <Text style={styles.loginText}>
              Already have an account? <Text style={styles.loginLink}>Sign in</Text>
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};


export default RegisterScreen;
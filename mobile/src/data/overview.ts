import { UserOverview } from '../types';

export const getOverviewById = (id: string): UserOverview => {
  return {
    userName: "<PERSON> Do<PERSON>",
    goal: "fat_loss",
    stats: {
      weight: 72,
      height: 178,
      age: 29,
    },
    progressOverview:
      "Making steady progress towards fitness goals. Keep up the good work!",
    avatarUrl: "",
    progress: {
      rate: 0,
    },
    startingWeight: 85,
    currentWeight: 80,
    startingFatPercentage: 20,
    currentFatPercentage: 15,
    engagementLevel: 75,
    progressLevel: 50,
    weeklyIntensity: 20,
    engagement: {
      lastLogin: "2025-04-01",
      sessionsThisWeek: 4,
      score: 0,
    },
    latestChanges: "תוכנית האימונים הותאמה לצרכי ההתאוששות",
    latestEvents: "השלים הערכת פיזיותרפיה",
  };
};

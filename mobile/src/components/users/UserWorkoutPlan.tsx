import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
} from "react-native";
import {
  AlertCircle,
  Calendar,
  ChevronDown,
  ChevronUp,
  Clock,
  Dumbbell,
  DumbbellIcon,
  Play,
  X,
} from "lucide-react-native";
import { trainingPlansApi } from "../../api/training.api";
import { useTheme } from "../../contexts/ThemeContext";
import { createStyles } from "../../style/UserWorkoutplans.style";
import WebView from "react-native-webview";
import { useTranslation } from "react-i18next";
import {
  GestureHandlerRootView,
  RefreshControl,
} from "react-native-gesture-handler";
import type {
  Day,
  UserWorkoutPlanProps,
  ExpandedExerciseStatsProps
} from "../../types";



// Extract YouTube video ID from URL
const getVideoId = (
  url: string | null | undefined
): { id: string | null; platform: "youtube" | "vimeo" | "unknown" } => {
  if (!url) return { id: null, platform: "unknown" };

  // YouTube (normal + shorts)
  const youtubeRegex =
    /(?:youtube\.com\/(?:watch\?v=|embed\/|shorts\/)|youtu\.be\/)([^?&\/\s]{11})/;
  const youtubeMatch = url.match(youtubeRegex);
  if (youtubeMatch) {
    return { id: youtubeMatch[1], platform: "youtube" };
  }

  // Vimeo (normal + player.vimeo)
  const vimeoRegex =
    /(?:vimeo\.com\/(?:.*\/)?|player\.vimeo\.com\/video\/)(\d+)/;
  const vimeoMatch = url.match(vimeoRegex);
  if (vimeoMatch) {
    return { id: vimeoMatch[1], platform: "vimeo" };
  }

  return { id: null, platform: "unknown" };
};

const getVideoEmbeddedUrl = (url: string | null | undefined): string | null => {
  const { id, platform } = getVideoId(url);

  if (!id) return null;

  switch (platform) {
    case "youtube":
      return `https://www.youtube.com/embed/${id}?autoplay=1&controls=1`;
    case "vimeo":
      return `https://player.vimeo.com/video/${id}?autoplay=1&title=0&byline=0&portrait=0`;
    default:
      return null;
  }
};

export const UserWorkoutPlan: React.FC<UserWorkoutPlanProps> = ({ userId }) => {
  const { t } = useTranslation();
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [trainingPlan, setTrainingPlan] = useState<Day[]>([]);
  const [selectedExerciseName, setSelectedExerciseName] = useState<string>("");
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [expandedDay, setExpandedDay] = useState<number>(-1); // set -1 to to make every plan day dropdown closed by deafult
  const [expandedExercise, setExpandedExercise] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const { isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");

  async function getTrainingPlans() {
    try {
      setLoading(true);
      const res = await trainingPlansApi.getById(userId);
      setTrainingPlan(res);
    } catch (error) {
      console.error("Error fetching training plans:", error);
      setError(t("errors.failedToLoadTrainingPlan"));
    } finally {
      setLoading(false);
    }
  }

  const openVideo = (videoUrl: string, exerciseName: string) => {
    setSelectedVideo(videoUrl);
    setSelectedExerciseName(exerciseName);
    setModalVisible(true);
  };

  const closeVideo = () => {
    setModalVisible(false);
    setTimeout(() => setSelectedVideo(null), 300);
  };

  const toggleExercise = (exerciseId: string) => {
    setExpandedExercise(expandedExercise === exerciseId ? null : exerciseId);
  };

  useEffect(() => {
    getTrainingPlans();
  }, [userId]);

  // Handle pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    setError(null);

    await getTrainingPlans();
    setRefreshing(false);
  }, [getTrainingPlans]);

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <Text style={styles.retryText} onPress={onRefresh}>
          {t("common.tapToRetry")}
        </Text>
      </View>
    );
  }

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={isDark ? "#E9D5FF" : "#3B82F6"} />
        <Text style={styles.loadingText}>{t("workoutPlan.loading")}.</Text>
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flexGrow: 1 }}>
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[isDark ? "#E9D5FF" : "#3B82F6"]} // Android
            tintColor={isDark ? "#E9D5FF" : "#3B82F6"} // iOS
          />
        }
      >
        {/* Video Modal */}
        <Modal
          visible={modalVisible}
          transparent={true}
          animationType="fade"
          onRequestClose={closeVideo}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={closeVideo}
            style={styles.modalContainer}
          >
            <TouchableOpacity
              activeOpacity={1}
              onPress={(e) => e.stopPropagation()}
              style={styles.modalContent}
            >
              <View style={styles.modalHeader}>
                <Text style={styles.modalExerciseName}>
                  {selectedExerciseName}
                </Text>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={closeVideo}
                >
                  <Text style={styles.closeButtonText}>
                    <X
                      color={isDark ? "#C4B5FD" : styles.closeButtonText.color}
                    />
                  </Text>
                </TouchableOpacity>
              </View>

              <View style={styles.videoPlayer}>
                {selectedVideo && getVideoEmbeddedUrl(selectedVideo) ? (
                  <WebView
                    source={{ uri: getVideoEmbeddedUrl(selectedVideo) || "" }}
                    allowsFullscreenVideo={true}
                    javaScriptEnabled={true}
                    domStorageEnabled={true}
                    mediaPlaybackRequiresUserAction={false}
                    allowsInlineMediaPlayback={true}
                    startInLoadingState={true}
                    automaticallyAdjustContentInsets={true}
                    useWebKit={true}
                    allowsAirPlayForMediaPlayback={true}
                    allowsPictureInPictureMediaPlayback={true}
                    mediaCapturePermissionGrantType="grant"
                  />
                ) : (
                  <Text style={{ color: "black", textAlign: "center" }}>
                    {t("workoutPlan.invalidVideo")}
                  </Text>
                )}
              </View>
            </TouchableOpacity>
          </TouchableOpacity>
        </Modal>

        <View style={styles.outerCard}>
          <View style={styles.trainingHeader}>
            <View style={styles.trainingHeaderTextContainer}>
              <Text style={styles.trainingTitle}>{t("workoutPlan.title")}</Text>

              <Text style={styles.trainingSubtitle}>
                {t("workoutPlan.subtitle")}
              </Text>
            </View>

            <View style={styles.editButton}>
              <Dumbbell
                color={isDark ? "rgba(216, 180, 254, 1)" : "#4A90E2"}
                size={22}
              />
            </View>
          </View>

          <View style={styles.tipBox}>
            <Text style={styles.tipText}>{t("workoutPlan.tip.details")}</Text>
            <Text style={styles.tipIcon}>{t("workoutPlan.tip.label")} </Text>
            <View
              style={{
                marginLeft: 5,
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <AlertCircle color="#f6bf22" size={12} />
            </View>
          </View>
        </View>

        {trainingPlan.length > 0 ? (
          trainingPlan.map((day, dayIndex) => (
            <View
              key={dayIndex}
              style={[
                styles.dayCard,
                expandedDay === dayIndex && styles.dayCardActive,
              ]}
            >
              {/* collapsed day view*/}
              <TouchableOpacity
                onPress={() =>
                  setExpandedDay(dayIndex === expandedDay ? -1 : dayIndex)
                }
                activeOpacity={0.8}
                style={styles.CollapsedDayCard}
              >
                <View style={styles.CollapsedDayUpperContainer}>
                  {expandedDay === dayIndex ? (
                    <View style={styles.chevronButtonStyles}>
                      <ChevronUp
                        color={isDark ? "#C4B5FD" : "#4A90E2"}
                        size={16}
                      />
                    </View>
                  ) : (
                    <View style={styles.chevronButtonStyles}>
                      <ChevronDown
                        color={isDark ? "#C4B5FD" : "#4A90E2"}
                        size={16}
                      />
                    </View>
                  )}

                  <Text style={styles.dayTitle}>
                    {day.day} - {day.focus}
                  </Text>

                  <View
                    style={[styles.chevronButtonStyles, styles.calendarIcon]}
                  >
                    <Calendar
                      color={isDark ? "#C4B5FD" : "#4A90E2"}
                      size={18}
                    />
                  </View>
                </View>

                <View style={styles.CollapsedDayLowerContainer}>
                  <Text style={styles.CollapsedDayLowerInfo}>
                    {expandedDay === dayIndex
                      ? t("workoutPlan.tip.collapsedExercise")
                      : t("workoutPlan.tip.collapsedDay")}
                  </Text>

                  <View
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "flex-end",
                      alignItems: "center",
                    }}
                  >
                    <Text style={styles.exerciseCount}>
                      {t("workoutPlan.exercises", {
                        count: day.exercises.length,
                      })}
                    </Text>
                    <AlertCircle color={styles.exerciseCount.color} size={9} />
                  </View>
                </View>
              </TouchableOpacity>

              {/* expanded day view*/}
              {expandedDay === dayIndex && (
                <View>
                  {day.exercises.map((exercise, exerciseIndex) => (
                    <View key={exerciseIndex} style={styles.exerciseCard}>
                      {/* Collapsed Exercise */}
                      <TouchableOpacity
                        activeOpacity={0.8}
                        style={styles.exerciseRow}
                        onPress={() => toggleExercise(exercise.id)}
                      >
                        <View style={styles.exerciseNameBtn}>
                          <Text style={styles.exerciseNameText}>
                            {exercise.name}
                          </Text>
                          {expandedExercise === exercise.id ? (
                            <ChevronUp
                              color={isDark ? "#C4B5FD" : "#4A90E2"}
                              size={18}
                            />
                          ) : (
                            <ChevronDown
                              color={isDark ? "#C4B5FD" : "#4A90E2"}
                              size={18}
                            />
                          )}
                        </View>

                        <View style={styles.statsRow}>
                          <View style={[styles.statPill, styles.statPillRest]}>
                            <Text style={styles.statRestText}>
                              {exercise.rest?.replace(/\D/g, "") || "80"}{" "}
                              <Clock
                                size={9}
                                color={styles.statRestText.color}
                              />
                            </Text>
                          </View>

                          <View style={[styles.statPill, styles.statPillReps]}>
                            <Text style={styles.statRepsText}>
                              {t("workoutPlan.reps")}{" "}
                              {exercise.reps?.replace(/[^0-9-]/g, "") || "80"}
                            </Text>
                          </View>

                          <View style={[styles.statPill, styles.statPillSets]}>
                            <Text style={styles.statSetsText}>
                              {t("workoutPlan.sets")}{" "}
                              {exercise.sets?.replace(/[^0-9-]/g, "") || "80"}
                            </Text>
                          </View>
                        </View>
                      </TouchableOpacity>

                      {/* Expanded Exercise */}
                      {expandedExercise === exercise.id && (
                        <View style={styles.exerciseDetails}>
                          <View style={styles.exerciseDetailsInnerContainer}>
                            {/* exercise details header */}
                            <View
                              style={{
                                ...styles.expandedExerciseHeaderContainer,
                                justifyContent: exercise.videoUrl
                                  ? "space-between"
                                  : "flex-end",
                              }}
                            >
                              {exercise.videoUrl && (
                                <TouchableOpacity
                                  style={styles.exerciseVideoButtton}
                                  onPress={() => {
                                    openVideo(
                                      exercise.videoUrl || "",
                                      exercise.name
                                    );
                                  }}
                                >
                                  <Text style={styles.exerciseVideoButttonText}>
                                    {t("workoutPlan.watchDemo")}
                                  </Text>
                                  <Play
                                    size={11}
                                    color={isDark ? "#C4B5FD" : "#4A90E2"}
                                  />
                                </TouchableOpacity>
                              )}

                              <View
                                style={styles.expandedExerciseNameContainer}
                              >
                                <Text
                                  style={styles.expandedExerciseDetailsName}
                                >
                                  {exercise.name}
                                </Text>
                                <DumbbellIcon
                                  size={15}
                                  color={isDark ? "#c084fc" : "#4A90E2"}
                                />
                              </View>
                            </View>

                            {/* exercise stats  */}
                            <View style={styles.exercisStatsExpandedContainer}>
                              <ExpandedExerciseStats
                                type="rest"
                                value={exercise.rest}
                                icon={Clock}
                              />
                              <ExpandedExerciseStats
                                type="reps"
                                value={exercise.reps}
                              />
                              <ExpandedExerciseStats
                                type="sets"
                                value={exercise.sets}
                              />
                            </View>

                            {/* exercise instructions  */}
                            {exercise.instructions && (
                              <View style={styles.instructionsContainer}>
                                <View
                                  style={styles.instructionsTextHeaderContainer}
                                >
                                  <Text style={styles.instructionsTextHeader}>
                                    {t("workoutPlan.instructionsHeader")}
                                  </Text>
                                  <AlertCircle color="#f6bf22" size={10} />
                                </View>
                                <Text style={styles.instructionsText}>
                                  {exercise.instructions}
                                </Text>
                              </View>
                            )}
                          </View>
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))
        ) : (
          <View style={styles.outerCard}>
            <Text style={styles.noTrainingPlanText}>
              {t("workoutPlan.noTrainingDays")}
            </Text>
          </View>
        )}
      </ScrollView>
    </GestureHandlerRootView>
  );
};



const ExpandedExerciseStats: React.FC<ExpandedExerciseStatsProps> = ({
  value,
  icon: Icon,
  type,
}) => {
  const { t } = useTranslation();
  const { isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");

  const typeStyles = {
    rest: styles.expandedStatPillRest,
    sets: styles.expandedStatPillSets,
    reps: styles.expandedStatPillReps,
  };

  return (
    <View style={[styles.expandedStatPill, typeStyles[type]]}>
      <Text
        style={{
          color: typeStyles[type].color,
          fontSize: 10,
        }}
      >
        {t(`workoutPlan.${type}`)}
      </Text>

      <View
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Text
          style={{
            fontSize: 12,
            color: isDark ? "white" : "black",
            marginRight: Icon ? 2 : 0,
          }}
        >
          {value?.replace(/[^0-9-]/g, "") || "80"}
        </Text>
        {Icon && <Icon size={9} color={typeStyles[type].color} />}
      </View>
    </View>
  );
};

import React, { createContext, useContext, useEffect, useState } from "react";
import {
  useColorScheme,
  AppState,
  AppStateStatus,
  Appearance,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ThemeType, ThemeContextType, ThemeColors } from "../types/theme";

const lightColors: ThemeColors = {
  background: "#FFFFFF",
  text: "#000000",
  primary: "#007AFF", // iOS blue
  secondary: "#5856D6",
  statusBar: "dark-content",
  navBar: "#F2F2F2",
};

const darkColors: ThemeColors = {
  background: "#000000",
  text: "#FFFFFF",
  primary: "#0A84FF", // iOS dark mode blue
  secondary: "#5E5CE6",
  statusBar: "light-content",
  navBar: "#1C1C1E",
};

const THEME_STORAGE_KEY = "app_theme_preference";

const ThemeContext = createContext<ThemeContextType>({
  theme: "system",
  isDark: false,
  setTheme: async () => {},
  colors: lightColors,
});

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const deviceColorScheme = useColorScheme();
  const [theme, setThemeState] = useState<ThemeType>("system");
  const [appState, setAppState] = useState<AppStateStatus>(
    AppState.currentState
  );

  // Track the current system appearance separately
  const [systemTheme, setSystemTheme] = useState<"light" | "dark">(
    deviceColorScheme || Appearance.getColorScheme() || "light"
  );

  // Load saved theme when component mounts
  useEffect(() => {
    loadSavedTheme();
  }, []);

  // Listen for system theme changes
  useEffect(() => {
    const appearanceListener = Appearance.addChangeListener(
      ({ colorScheme }) => {
        setSystemTheme((colorScheme as "light" | "dark") || "light");
      }
    );

    // Initial system theme setup
    setSystemTheme(
      (Appearance.getColorScheme() as "light" | "dark") || "light"
    );

    return () => {
      appearanceListener.remove();
    };
  }, []);

  // Listen for app state changes
  useEffect(() => {
    const subscription = AppState.addEventListener("change", (nextAppState) => {
      if (appState.match(/inactive|background/) && nextAppState === "active") {
        // App has come to the foreground, check if system theme changed
        const currentColorScheme = Appearance.getColorScheme();
        setSystemTheme((currentColorScheme as "light" | "dark") || "light");
      }
      setAppState(nextAppState);
    });

    return () => {
      subscription.remove();
    };
  }, [appState]);

  // React to deviceColorScheme changes from useColorScheme hook
  useEffect(() => {
    if (deviceColorScheme) {
      setSystemTheme(deviceColorScheme);
    }
  }, [deviceColorScheme]);

  const loadSavedTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (
        savedTheme &&
        (savedTheme === "light" ||
          savedTheme === "dark" ||
          savedTheme === "system")
      ) {
        setThemeState(savedTheme as ThemeType);
      }
    } catch (error) {
      console.error("Error loading theme:", error);
    }
  };

  const setTheme = async (newTheme: ThemeType) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, newTheme);
      setThemeState(newTheme);
    } catch (error) {
      console.error("Error saving theme:", error);
    }
  };

  // Calculate if dark mode is active based on theme setting and system state
  const isDark = theme === "system" ? systemTheme === "dark" : theme === "dark";

  // Select the appropriate color palette based on the current theme
  const colors = isDark ? darkColors : lightColors;

  return (
    <ThemeContext.Provider value={{ theme, isDark, setTheme, colors }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};

import React, { useState } from "react";
import { Coffee, Save, X, Check } from "lucide-react";
import { ErrorAlert } from "../training/ErrorAlert";

interface CustomFoodInputProps {
  isLoading: boolean;
  error: any;
  onSave: (food: {
    name: string;
    category: "protein" | "carbs" | "other";
    minServing: number;
    maxServing: number;
    macrosPer100g: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
    defaultServing: number;
  }) => Promise<void>;
  onClose: () => void;
}

export const CustomFoodInput: React.FC<CustomFoodInputProps> = ({
  isLoading,
  error,
  onSave,
  onClose,
}) => {
  const [name, setName] = useState("");
  const [category, setCategory] = useState<"protein" | "carbs" | "other">(
    "other"
  );
  const [defaultServing, setDefaultServing] = useState(100);
  const [macrosPer100g, setMacrosPer100g] = useState({
    protein: 0,
    carbs: 0,
    fats: 0,
    calories: 0,
  });
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSave = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!name.trim()) return;

    if (defaultServing <= 0) return;

    try {
      const minServing = defaultServing - 30 < 0 ? 0 : defaultServing - 30;
      const maxServing = defaultServing + 30;

      await onSave({
        name,
        category,
        minServing,
        maxServing,
        macrosPer100g,
        defaultServing,
      });

      setShowSuccess(true);

      setTimeout(() => {
        setShowSuccess(false);
        onClose();
      }, 1500);
    } catch (error) {
      console.error({ error });
    }
  };

  return (
    <div className="bg-gray-900 rounded-xl p-6 shadow-lg border-2 border-purple-800">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-purple-900">
            <Coffee className="h-5 w-5 text-purple-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-200">
            הוספת מזון מותאם אישית
          </h3>
        </div>
        <button
          type="button"
          onClick={onClose}
          disabled={isLoading}
          className="p-1 hover:bg-gray-800 rounded-full transition-colors duration-200"
        >
          <X className="h-5 w-5 text-gray-400" />
        </button>
      </div>
      {showSuccess && (
        <div className="mb-4 p-3 bg-green-900 text-green-300 rounded-lg flex items-center gap-2">
          <Check className="h-5 w-5" />
          <span>המזון נוסף בהצלחה!</span>
        </div>
      )}

      {error && (
        <ErrorAlert
          message={
            (error as { data?: { message?: string } })?.data?.message ||
            "אירעה שגיאה"
          }
        />
      )}

      <form className="space-y-4" onSubmit={handleSave}>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            שם המזון
          </label>
          <input
            type="text"
            value={name}
            disabled={isLoading}
            required
            onChange={(e) => setName(e.target.value)}
            className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            placeholder="הזן שם..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            קטגוריה
          </label>
          <select
            value={category}
            disabled={isLoading}
            onChange={(e) =>
              setCategory(e.target.value as "protein" | "carbs" | "other")
            }
            className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
          >
            <option value="protein">חלבון</option>
            <option value="carbs">פחמימה</option>
            <option value="other">אחר</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            כמות (גרם)
          </label>
          <input
            type="number"
            value={defaultServing}
            min={10}
            disabled={isLoading}
            onChange={(e) => setDefaultServing(Number(e.target.value))}
            className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              חלבון (גרם)
            </label>
            <input
              type="number"
              value={macrosPer100g.protein}
              min={10}
              disabled={isLoading}
              onChange={(e) =>
                setMacrosPer100g({
                  ...macrosPer100g,
                  protein: Number(e.target.value),
                })
              }
              className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              פחמימות (גרם)
            </label>
            <input
              type="number"
              value={macrosPer100g.carbs}
              min={10}
              disabled={isLoading}
              onChange={(e) =>
                setMacrosPer100g({
                  ...macrosPer100g,
                  carbs: Number(e.target.value),
                })
              }
              className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              שומן (גרם)
            </label>
            <input
              type="number"
              value={macrosPer100g.fats}
              min={10}
              disabled={isLoading}
              onChange={(e) =>
                setMacrosPer100g({
                  ...macrosPer100g,
                  fats: Number(e.target.value),
                })
              }
              className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              קלוריות
            </label>
            <input
              type="number"
              value={macrosPer100g.calories}
              min={10}
              disabled={isLoading}
              onChange={(e) =>
                setMacrosPer100g({
                  ...macrosPer100g,
                  calories: Number(e.target.value),
                })
              }
              className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            />
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-gray-300 transition-colors duration-200"
          >
            ביטול
          </button>

          <button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2 px-4 py-2 bg-purple-700 text-gray-200 rounded-lg hover:bg-purple-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-5 w-5" />
            <span>שמור מזון</span>
          </button>
        </div>
      </form>
    </div>
  );
};

import React, { useState } from "react";
import { FoodItem } from "../../types/food";
import { Plus } from "lucide-react";
import { CustomFoodInput } from "./CustomFoodInput";
import { useCreateCustomFoodItemsMutation } from "../../api/services/Trainer/TrainerService";

interface FoodCategorySelectorProps {
  category: string;
  onFoodSelect: (food: FoodItem) => void;
  assignedFoods?: string[];
  foodItems: FoodItem[];
  setFoodItems: React.Dispatch<React.SetStateAction<FoodItem[]>>;
}

export const FoodCategorySelector: React.FC<FoodCategorySelectorProps> = ({
  category,
  onFoodSelect,
  assignedFoods = [],
  foodItems,
  setFoodItems,
}) => {
  const [showCustomInput, setShowCustomInput] = useState(false);

  const getFoodsByCategory = (category: string): FoodItem[] | null => {
    if (foodItems && Array.isArray(foodItems) && foodItems.length > 0) {
      const standardFoods = foodItems.filter(
        (food) => food.category === category
      );
      return [...standardFoods].sort((a, b) => a.name.localeCompare(b.name));
    } else {
      return null;
    }
  };

  const foods = getFoodsByCategory(category);

  const [createCustomFoodItems, { isLoading, error }] =
    useCreateCustomFoodItemsMutation();

  const handleCustomFoodSave = async (customFood: {
    name: string;
    category: "protein" | "carbs" | "other";
    minServing: number;
    maxServing: number;
    macrosPer100g: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
    defaultServing: number;
  }) => {
    try {
      const newFoodItem = await createCustomFoodItems(customFood).unwrap();

      setFoodItems([...foodItems, newFoodItem]);
    } catch (error) {
      console.error({ error });
    }
  };

  return (
    <div className="relative">
      <div className="flex items-center gap-2">
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <Plus className="h-4 w-4 text-purple-400" />
        </div>
        <select
          className="w-full p-2 pr-8 pl-4 border border-gray-700 rounded-lg appearance-none bg-gray-800 text-gray-300 cursor-pointer hover:border-purple-600 transition-colors duration-200 focus:border-purple-500 focus:ring focus:ring-purple-700 focus:ring-opacity-50"
          onChange={(e) => {
            if (e.target.value === "custom") {
              setShowCustomInput(true);
            } else {
              const food = foods && foods.find((f) => f.id === e.target.value);
              if (food) onFoodSelect(food);
            }
            e.target.value = "";
          }}
          defaultValue=""
        >
          <option value="" disabled className="bg-gray-800 text-gray-400">
            {category === "protein"
              ? "הוסף חלבון"
              : category === "carbs"
              ? "הוסף פחמימה"
              : "הוסף מזון"}
          </option>
          <option value="custom" className="text-purple-400 bg-gray-800">
            ➕ הוסף מזון מותאם אישית
          </option>
          <optgroup label="מזונות קיימים" className="bg-gray-800 text-gray-300">
            {foods &&
              foods.map((food) => {
                const isAssigned = assignedFoods.includes(food.id);
                return (
                  <option
                    key={food.id}
                    value={food.id}
                    disabled={isAssigned}
                    className={
                      isAssigned
                        ? "text-gray-500 bg-gray-700"
                        : "text-gray-300 bg-gray-800"
                    }
                  >
                    {food.name} {isAssigned ? "(מוקצה)" : ""}
                  </option>
                );
              })}
          </optgroup>
        </select>
      </div>

      {showCustomInput && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="max-w-2xl w-full">
            <CustomFoodInput
              isLoading={isLoading}
              error={error}
              onSave={handleCustomFoodSave}
              onClose={() => setShowCustomInput(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

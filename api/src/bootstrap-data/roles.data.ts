import { ROLE_VALUES } from 'src/models/user-entity';

export const RolesList = [
  {
    name: 'Trainer',
    value: ROLE_VALUES.TRAINER, // Ensure this exists
    description: 'Manages training sessions',
    permissionValueList: ['PERMISSION_CREATE', 'PERMISSION_EDIT'],
  },
  {
    name: 'Trainee',
    value: ROLE_VALUES.TRAINEE, // Ensure this exists
    description: 'Attends training sessions',
    permissionValueList: ['PERMISSION_VIEW'],
  },
  {
    name: 'Admin',
    value: ROLE_VALUES.ADMIN, // Ensure this exists
    description: 'Has full access to the system',
    permissionValueList: [
      'PERMISSION_CREATE',
      'PERMISSION_EDIT',
      'PERMISSION_DELETE',
      'PERMISSION_VIEW',
    ],
  },
];

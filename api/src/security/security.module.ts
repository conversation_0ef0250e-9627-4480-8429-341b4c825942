import { Module, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoggerModule } from 'src/common/logger/logger.module';
import { FilterModule } from 'src/common/filters/filter.module';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { JwtAuthGuard } from './middleware/authGuard.middleware';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { ConfigModule } from 'src/common/configuration/config.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserEntity, AccessTokenEntity, RoleEntity]),
    LoggerModule,
    FilterModule,
    ConfigModule,
    JwtModule.register({}),
  ],
  providers: [JwtAuthGuard, JwtService],
  controllers: [],
  exports: [JwtAuthGuard, JwtService, TypeOrmModule],
})
export class SecurityModule implements NestModule {
  public configure() {}
}

import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { UserEntity } from '../user-entity';

export type MacroNutrients = {
  protein: number;
  carbs: number;
  fats: number;
  calories: number;
};

export type MacroRange = {
  min: MacroNutrients;
  max: MacroNutrients;
};

@Entity('food_items')
export class FoodItemEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  category: string; // 'protein', 'carbs', etc.

  @Column('int')
  defaultServing: number;

  @Column('int', { nullable: true })
  minServing: number;

  @Column('int', { nullable: true })
  maxServing: number;

  @Column('jsonb')
  macrosPer100g: {
    protein: number;
    carbs: number;
    fats: number;
    calories: number;
  };

  @ManyToOne(() => UserEntity, (creator) => creator.foodItems, {
    nullable: true,
  })
  @JoinColumn({ name: 'creatorId' })
  creator: UserEntity | null = null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

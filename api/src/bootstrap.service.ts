import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import {
  ExercisesList,
  FoodItemsList,
  PermissionsList,
  RolesList,
} from './bootstrap-data';
import { Injectable } from '@nestjs/common';
import { PermissionEntity, RoleEntity, UserEntity } from './models/user-entity';
import { FoodItemEntity } from './models/meals/food-items.entity';
import { CustomLogger } from './common/logger/custom-logger.service';
import { ROLE_VALUES } from './models/user-entity/role.entity';
import { AuthService } from './auth/auth.service';
import { FirebaseService } from './third-party/firebase/firebase-authentication.service';
import { ExerciseEntity } from './models/excercise/exercises.entity';
import { ErrorResponse } from './utils/responses';

interface AdminData {
  NAME: string;
  EMAIL: string;
  PASSWORD: string;
}

@Injectable()
export class BootstrapService {
  constructor(
    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,

    @InjectRepository(PermissionEntity)
    private readonly permissionRepo: Repository<PermissionEntity>,

    @InjectRepository(FoodItemEntity)
    private readonly foodItemsRepo: Repository<FoodItemEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(ExerciseEntity)
    private readonly exerciseRepo: Repository<ExerciseEntity>,

    private readonly logger: CustomLogger,
    private readonly authService: AuthService,
    private readonly firebaseService: FirebaseService,
  ) {}

  async InitializeBootstrap() {
    // Create permissions and roles first
    await this.createPermissions();
    await this.createRoles();

    // Then create admin user (which depends on roles)
    await this.createAdmin();

    // Create other data
    await this.createFoodItems();
    await this.createExercises();
  }

  private getAdminData(): AdminData {
    return {
      NAME: process.env.APP_ADMIN_NAME || '',
      EMAIL: process.env.APP_ADMIN_MAIL || '',
      PASSWORD: process.env.APP_ADMIN_PASSWORD || '',
    };
  }

  private async createAdmin() {
    const { EMAIL, NAME, PASSWORD } = this.getAdminData();

    if (!EMAIL || !NAME || !PASSWORD) {
      const CustomError: ErrorResponse = {
        error: true,
        statusCode: 400,
        message: 'Admin credentials are not fully provided...',
        path: '/bootstrap/createAdmin', // Add this dynamically if necessary
        errorId: 1, // A generated ID or timestamp
        timestamp: new Date(), // Populate timestamp
      };

      this.logger.error(CustomError);
      return;
    }

    let user: UserEntity = await this.userRepo.findOne({
      where: { email: EMAIL },
    });

    if (user == null) {
      // Check if admin role exists
      let role: RoleEntity = await this.roleRepo.findOne({
        where: { value: ROLE_VALUES.ADMIN },
      });

      // If role doesn't exist, create it
      if (!role) {
        console.log('Admin role not found. Creating admin role...');
        // Create admin role with basic permissions
        const permissions = await this.permissionRepo.find();
        role = this.roleRepo.create({
          name: 'Admin',
          value: ROLE_VALUES.ADMIN,
          description: 'Has full access to the system',
          permissions: permissions,
        });

        try {
          role = await this.roleRepo.save(role);
          console.log('Admin role created successfully.');
        } catch (error) {
          console.error('Error creating admin role:', error);
          return;
        }
      }

      let AdminFirebaseUUid =
        await this.firebaseService.getUserUidByEmail(EMAIL);

      if (AdminFirebaseUUid === null) {
        const firebaseUser = await this.firebaseService.createUser(
          EMAIL,
          PASSWORD,
        );

        AdminFirebaseUUid = firebaseUser.uid;
      }

      user = new UserEntity();
      user.id = AdminFirebaseUUid;
      user.email = EMAIL;
      user.name = NAME;
      user.isActive = true;
      user.passwordHash = await this.authService.hashPassword(PASSWORD);

      // Make sure role exists before assigning
      if (role && role.id) {
        user.role = role;
        user.roleId = role.id;
      } else {
        console.error(
          'Admin role could not be created or found. Cannot create admin user.',
        );
        return;
      }

      try {
        await this.userRepo.save(user);
        console.log('Admin created successfully.');
      } catch (error) {
        console.error('Error saving admin user:', error);
        throw error;
      }
    } else {
      console.log('Admin already exists in DB.');
    }
  }

  private async createRoles() {
    try {
      const existingRoles = await this.roleRepo.find({ select: ['value'] });
      const existingRoleValues = new Set(
        existingRoles.map((role) => role.value),
      );

      const newRoles = [];

      for (const role of RolesList) {
        if (!existingRoleValues.has(role.value)) {
          const permissions = await this.permissionRepo.find({
            where: {
              value: In(role.permissionValueList),
            },
          });

          const newRole = this.roleRepo.create({
            name: role.name,
            value: role.value,
            description: role.description,
            permissions: permissions,
          });

          newRoles.push(newRole);
        }
      }

      if (newRoles.length > 0) {
        await this.roleRepo.save(newRoles);
        console.log(`Successfully inserted ${newRoles.length} new roles.`);
      } else {
        console.log('No new roles to insert. All roles already exist.');
      }
    } catch (error) {
      console.error('Error creating roles:', error);
    }
  }

  private async createPermissions() {
    try {
      const existingPermissions = await this.permissionRepo.find();

      if (existingPermissions.length === 0) {
        await this.permissionRepo.save(PermissionsList);
        console.log('Permissions initialized successfully.');
      } else {
        console.log('Permissions already exist.');
      }
    } catch (error) {
      console.error('Error creating permissions:', error);
    }
  }

  private async createFoodItems() {
    try {
      const existingFoodItems = await this.foodItemsRepo.find();

      if (existingFoodItems.length === 0) {
        const newFoodItems = this.foodItemsRepo.create(FoodItemsList);
        await this.foodItemsRepo.save(newFoodItems);
        console.log(`Successfully inserted ${newFoodItems.length} food items.`);
      } else {
        console.log('Food items already exist. No new items inserted.');
      }
    } catch (error) {
      console.error('Error creating food items:', error);
    }
  }

  private async createExercises() {
    try {
      const existingExercises = await this.exerciseRepo.find();

      if (existingExercises.length === 0) {
        const newExercise = this.exerciseRepo.create(ExercisesList);
        await this.exerciseRepo.save(newExercise);
        console.log(`Successfully inserted ${newExercise.length} exercises.`);
      } else {
        console.log('Exercises already exist. No new items inserted.');
      }
    } catch (error) {
      console.error('Error creating Exercises:', error);
    }
  }
}

import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainerProfileEntity } from 'src/models/profiles';
import {
  AccessTokenEntity,
  OTPEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { JwtService } from '@nestjs/jwt';
import { FirebaseService } from 'src/third-party/firebase/firebase-authentication.service';
import { VerificationEmailService } from 'src/common/email/send-verification-email.service';
import { SesService } from 'src/third-party/aws/SES/ses.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      RoleEntity,
      TrainerProfileEntity,
      AccessTokenEntity,
      OTPEntity,
    ]),
  ],
  controllers: [AdminController],
  providers: [
    AdminService,
    JwtService,
    FirebaseService,
    VerificationEmailService,
    SesService,
  ],
})
export class AdminModule {}

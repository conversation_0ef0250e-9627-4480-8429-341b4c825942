import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { RolesGuard } from 'src/security/middleware/rolesGuard.middleware';
import { TrainerAssignmentsService } from './trainee.service';
import { ROLE_VALUES } from 'src/models/user-entity';
import { Roles } from 'src/security/middleware/roles.decorator';
import { CreateTrainerAssignmentDto, UpdateTrainerAssignmentDto } from './dto';
import { TrainerAssignmentEntity } from 'src/models/profiles';

@ApiTags('trainer-assignments')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('trainer-assignments')
export class TrainerAssignmentsController {
  constructor(
    private readonly trainerAssignmentsService: TrainerAssignmentsService,
  ) {}

  @Post()
  @Roles(ROLE_VALUES.TRAINER)
  @ApiOperation({ summary: 'Create a new trainer assignment' })
  @ApiBody({ type: CreateTrainerAssignmentDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'The trainer assignment has been successfully created.',
    schema: {
      properties: {
        message: {
          type: 'string',
          example: 'Trainer assignment created successfully',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Trainer or trainee not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async create(@Body() createDto: CreateTrainerAssignmentDto) {
    return this.trainerAssignmentsService.createAssignment(createDto);
  }

  @Get()
  @Roles(ROLE_VALUES.TRAINER)
  @ApiOperation({ summary: 'Get all trainer assignments' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of all trainer assignments',
    type: [TrainerAssignmentEntity],
  })
  async findAll() {
    return this.trainerAssignmentsService.getAllAssignments();
  }

  @Get(':id')
  @Roles(ROLE_VALUES.TRAINER, ROLE_VALUES.TRAINEE)
  @ApiOperation({ summary: 'Get a trainer assignment by ID' })
  @ApiParam({ name: 'id', description: 'Trainer assignment ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The found trainer assignment',
    type: TrainerAssignmentEntity,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Trainer assignment not found',
  })
  async findOne(@Param('id') id: string) {
    return this.trainerAssignmentsService.getAssignmentById(id);
  }

  @Get('trainer/:trainerId')
  @Roles(ROLE_VALUES.TRAINER)
  @ApiOperation({ summary: 'Get all assignments for a specific trainer' })
  @ApiParam({ name: 'trainerId', description: 'Trainer user ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of assignments for the trainer',
    type: [TrainerAssignmentEntity],
  })
  async findByTrainer(@Param('trainerId') trainerId: string) {
    return this.trainerAssignmentsService.getAssignmentsByTrainerId(trainerId);
  }

  @Get('trainee/:traineeId')
  @Roles(ROLE_VALUES.TRAINER, ROLE_VALUES.TRAINEE)
  @ApiOperation({ summary: 'Get all assignments for a specific trainee' })
  @ApiParam({ name: 'traineeId', description: 'Trainee user ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of assignments for the trainee',
    type: [TrainerAssignmentEntity],
  })
  async findByTrainee(@Param('traineeId') traineeId: string) {
    return this.trainerAssignmentsService.getAssignmentsByTraineeId(traineeId);
  }

  @Put(':id')
  @Roles(ROLE_VALUES.TRAINER)
  @ApiOperation({ summary: 'Update a trainer assignment' })
  @ApiParam({ name: 'id', description: 'Trainer assignment ID' })
  @ApiBody({ type: UpdateTrainerAssignmentDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The trainer assignment has been successfully updated.',
    schema: {
      properties: {
        message: {
          type: 'string',
          example: 'Trainer assignment updated successfully',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Trainer assignment not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateTrainerAssignmentDto,
  ) {
    return this.trainerAssignmentsService.updateAssignment(id, updateDto);
  }

  @Delete(':id')
  @Roles(ROLE_VALUES.TRAINER)
  @ApiOperation({ summary: 'Delete a trainer assignment' })
  @ApiParam({ name: 'id', description: 'Trainer assignment ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The trainer assignment has been successfully deleted.',
    schema: {
      properties: {
        message: {
          type: 'string',
          example: 'Trainer assignment deleted successfully',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Trainer assignment not found',
  })
  async remove(@Param('id') id: string) {
    return this.trainerAssignmentsService.deleteAssignment(id);
  }
}

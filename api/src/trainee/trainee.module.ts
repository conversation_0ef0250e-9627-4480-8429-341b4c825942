import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  TraineeProfileEntity,
  TrainerAssignmentEntity,
} from 'src/models/profiles';
import {
  AccessTokenEntity,
  OTPEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { TrainerAssignmentsController } from './trainee.controller';
import { TrainerAssignmentsService } from './trainee.service';
import { JwtService } from '@nestjs/jwt';
import { FirebaseService } from 'src/third-party/firebase/firebase-authentication.service';
import { VerificationEmailService } from 'src/common/email/send-verification-email.service';
import { SesService } from 'src/third-party/aws/SES/ses.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TrainerAssignmentEntity,
      UserEntity,
      AccessTokenEntity,
      RoleEntity,
      OTPEntity,
      TraineeProfileEntity,
    ]),
  ],
  controllers: [TrainerAssignmentsController],
  providers: [
    TrainerAssignmentsService,
    JwtService,
    FirebaseService,
    VerificationEmailService,
    SesService,
  ],
})
export class TraineeModule {}

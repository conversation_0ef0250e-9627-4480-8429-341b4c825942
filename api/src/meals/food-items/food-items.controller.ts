import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { FoodItemsService } from './food-items.service';
import { FoodItemEntity } from 'src/models/meals';
import {
  CalculateMacrosDto,
  CreateFoodItemDto,
  UpdateFoodItemDto,
} from './dto';
import { RolesGuard } from 'src/security/middleware/rolesGuard.middleware';
import { Roles } from 'src/security/middleware/roles.decorator';
import { ROLE_VALUES } from 'src/models/user-entity';

@Controller('food-items')
@UseGuards(JwtAuthGuard, RolesGuard)
export class FoodItemsController {
  constructor(private readonly foodItemsService: FoodItemsService) {}

  @Get()
  findAll(@Query('category') category?: string): Promise<FoodItemEntity[]> {
    return this.foodItemsService.findAll(category);
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<FoodItemEntity> {
    return this.foodItemsService.findOne(id);
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Get('/trainer/:trainerId')
  GetAllTrainerFoodItems(
    @Param('trainerId') trainerId: string,
  ): Promise<FoodItemEntity[]> {
    return this.foodItemsService.getAllTrainerFoodItems(trainerId);
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Post()
  createCustomFoodItem(
    @Body() foodItem: CreateFoodItemDto,
    @Request() req,
  ): Promise<FoodItemEntity> {
    const user = req.user;
    return this.foodItemsService.create(foodItem, user);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() foodItem: UpdateFoodItemDto,
  ): Promise<FoodItemEntity> {
    return this.foodItemsService.update(id, foodItem);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.foodItemsService.remove(id);
  }

  @Post('macros')
  async calculateMacros(@Body() calculateMacrosDto: CalculateMacrosDto) {
    const { foodId, amount } = calculateMacrosDto;
    const foodItem = await this.foodItemsService.findOne(foodId);
    if (!foodItem) {
      return { error: 'Food item not found' };
    }
    return this.foodItemsService.calculateMacros(foodItem, amount);
  }
}

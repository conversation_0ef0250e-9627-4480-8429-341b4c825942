import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FoodItemEntity } from 'src/models/meals';
import { ROLE_VALUES, UserEntity } from 'src/models/user-entity';
import { MacroNutrients } from 'src/utils/interfaces';
import { In, Repository } from 'typeorm';

@Injectable()
export class FoodItemsService {
  constructor(
    @InjectRepository(FoodItemEntity)
    private foodItemsRepository: Repository<FoodItemEntity>,

    @InjectRepository(UserEntity)
    private userRepository: Repository<UserEntity>,
  ) {}

  async findAll(category?: string): Promise<FoodItemEntity[]> {
    if (category) {
      return this.foodItemsRepository.find({ where: { category } });
    }
    return this.foodItemsRepository.find();
  }

  async findOne(id: string): Promise<FoodItemEntity> {
    return this.foodItemsRepository.findOne({ where: { id } });
  }

  async findByIds(ids: string[]): Promise<FoodItemEntity[]> {
    if (!ids || ids.length === 0) return [];
    return this.foodItemsRepository.find({
      where: { id: In(ids) },
    });
  }

  async getAllTrainerFoodItems(trainerId: string): Promise<FoodItemEntity[]> {
    const trainer = await this.userRepository.findOne({
      where: { id: trainerId },
      relations: ['role'],
    });

    if (!trainer) {
      throw new BadRequestException('Trainer not found !!');
    }

    // Check if user has the trainer role
    const isTrainer = trainer.role.value === ROLE_VALUES.TRAINER;

    if (!isTrainer) {
      throw new BadRequestException('Only trainers can fetch food item !!');
    }

    return this.foodItemsRepository
      .createQueryBuilder('foodItem')
      .leftJoinAndSelect('foodItem.creator', 'creator')
      .where('creator.id = :trainerId', { trainerId: trainer.id })
      .orWhere('foodItem.creator IS NULL')
      .getMany();
  }

  async create(
    foodItem: Partial<FoodItemEntity>,
    user: UserEntity,
  ): Promise<FoodItemEntity> {
    const newFoodItem = this.foodItemsRepository.create({
      ...foodItem,
      creator: user,
    });

    return this.foodItemsRepository.save(newFoodItem);
  }

  async update(
    id: string,
    foodItem: Partial<FoodItemEntity>,
  ): Promise<FoodItemEntity> {
    await this.foodItemsRepository.update(id, foodItem);
    return this.foodItemsRepository.findOne({ where: { id } });
  }

  async remove(id: string): Promise<void> {
    await this.foodItemsRepository.delete(id);
  }

  calculateMacros(foodItem: FoodItemEntity, amount: number): MacroNutrients {
    const factor = amount / 100; // Convert to proportion of 100g
    return {
      protein: foodItem.macrosPer100g.protein * factor,
      carbs: foodItem.macrosPer100g.carbs * factor,
      fats: foodItem.macrosPer100g.fats * factor,
      calories: foodItem.macrosPer100g.calories * factor,
    };
  }
}

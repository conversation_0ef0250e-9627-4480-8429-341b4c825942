import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MealCategoryEntity, MealOptionEntity } from 'src/models/meals';
import { MealsCategoriesController } from './meals-categories.controller';
import { MealCategoriesService } from './meal-categories.service';

@Module({
  imports: [TypeOrmModule.forFeature([MealCategoryEntity, MealOptionEntity])],
  controllers: [MealsCategoriesController],
  providers: [MealCategoriesService],
  exports: [MealCategoriesService, TypeOrmModule],
})
export class MealsCategoriesModule {}

import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MealTemplateEntity } from 'src/models/meals';
import { Repository } from 'typeorm';
import {
  CreateMealTemplateDto,
  MealTemplateResponseDto,
  UpdateMealTemplateDto,
} from './dtos';

@Injectable()
export class MealTemplateService {
  constructor(
    @InjectRepository(MealTemplateEntity)
    private readonly repo: Repository<MealTemplateEntity>,
  ) {}

  async getAll(trainerId: string): Promise<MealTemplateResponseDto[]> {
    const templates = await this.repo.find({
      where: { trainerId },
      order: { createdAt: 'DESC' },
    });

    return templates.map(MealTemplateResponseDto.transform);
  }

  async getOne(
    id: string,
    trainerId: string,
  ): Promise<MealTemplateResponseDto> {
    const template = await this.repo.findOneBy({ id, trainerId });
    if (!template) throw new NotFoundException('Template not found');
    return MealTemplateResponseDto.transform(template);
  }

  async create(dto: CreateMealTemplateDto): Promise<MealTemplateResponseDto> {
    if (dto.templateData.length <= 0) {
      throw new BadRequestException('At least add one meal to meal plan.');
    }

    const template = this.repo.create({
      name: dto.name,
      trainerId: dto.trainerId,
      templateData: dto.templateData,
    });

    const saved = await this.repo.save(template);
    return MealTemplateResponseDto.transform(saved);
  }

  async update(
    id: string,
    trainerId: string,
    dto: UpdateMealTemplateDto,
  ): Promise<MealTemplateResponseDto> {
    const template = await this.repo.findOneBy({ id, trainerId });
    if (!template) throw new NotFoundException('Template not found');

    // Update allowed fields
    if (dto.name !== undefined) {
      template.name = dto.name;
    }

    // If templateData is provided in the update DTO in the future
    if ('templateData' in dto && dto.templateData) {
      template.templateData = dto.templateData;
    }

    const updated = await this.repo.save(template);
    return MealTemplateResponseDto.transform(updated);
  }

  async delete(id: string, trainerId: string): Promise<void> {
    const result = await this.repo.delete({ id, trainerId });
    if (result.affected === 0)
      throw new NotFoundException('Template not found');
  }
}

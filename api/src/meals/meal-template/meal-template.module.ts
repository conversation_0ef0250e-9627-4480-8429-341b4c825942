import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MealTemplateEntity } from 'src/models/meals';
import { UserEntity } from 'src/models/user-entity';
import { SecurityModule } from 'src/security/security.module';
import { MealTemplateController } from './meal-template.controller';
import { MealTemplateService } from './meal-template.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([MealTemplateEntity, UserEntity]),
    SecurityModule,
  ],
  controllers: [MealTemplateController],
  providers: [MealTemplateService],
  exports: [MealTemplateService],
})
export class MealTemplateModule {}

import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { RolesGuard } from 'src/security/middleware/rolesGuard.middleware';
import { MealTemplateService } from './meal-template.service';
import { ROLE_VALUES } from 'src/models/user-entity';
import { Roles } from 'src/security/middleware/roles.decorator';
import {
  CreateMealTemplateDto,
  MealTemplateResponseDto,
  UpdateMealTemplateDto,
} from './dtos';

@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('meal-templates')
export class MealTemplateController {
  constructor(private readonly service: MealTemplateService) {}

  @Roles(ROLE_VALUES.TRAINER)
  @Get()
  getAll(@Request() req): Promise<MealTemplateResponseDto[]> {
    const trainerId = req.user.id;
    return this.service.getAll(trainerId);
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Get(':id')
  getOne(
    @Param('id') id: string,
    @Request() req,
  ): Promise<MealTemplateResponseDto> {
    const trainerId = req.user.id;
    return this.service.getOne(id, trainerId);
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Post()
  create(
    @Request() req,
    @Body() dto: CreateMealTemplateDto,
  ): Promise<MealTemplateResponseDto> {
    const trainerId = req.user.id;
    return this.service.create({ ...dto, trainerId });
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Put(':id')
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() dto: UpdateMealTemplateDto,
  ): Promise<MealTemplateResponseDto> {
    const trainerId = req.user.id;
    return this.service.update(id, trainerId, dto);
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Delete(':id')
  delete(@Param('id') id: string, @Request() req): Promise<void> {
    const trainerId = req.user.id;
    return this.service.delete(id, trainerId);
  }
}

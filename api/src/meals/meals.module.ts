import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  FoodItemEntity,
  MealCategoryEntity,
  MealEntity,
  MealOptionEntity,
  MealTemplateEntity,
} from 'src/models/meals';
import { UserEntity } from 'src/models/user-entity';
import { SecurityModule } from 'src/security/security.module';
import { FoodItemsModule } from './food-items/food-items.module';
import { MealsCategoriesModule } from './meal-categories/meals-categories.module';
import { MealOptionModule } from './meal-option/meal-option.module';
import { MealTemplateModule } from './meal-template/meal-template.module';
import { MealsService } from './meals.service';
import { FoodItemsService } from './food-items/food-items.service';
import { MealsController } from './meals.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      MealEntity,
      MealCategoryEntity, // Ensure this is imported
      MealOptionEntity,
      FoodItemEntity,
      UserEntity,
      MealTemplateEntity,
    ]),
    SecurityModule,
    FoodItemsModule,
    MealsCategoriesModule,
    MealOptionModule,
    MealTemplateModule,
  ],
  providers: [MealsService, FoodItemsService],
  controllers: [MealsController],
  exports: [MealsService],
})
export class MealsModule {}

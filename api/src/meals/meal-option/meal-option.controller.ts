import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { MealOptionsService } from './meal-option.service';
import { MealOptionEntity } from 'src/models/meals';
import { CreateMealOptionDto, UpdateMealOptionDto } from './dto';

@Controller('meal-options')
@UseGuards(JwtAuthGuard)
export class MealOptionsController {
  constructor(private readonly mealOptionsService: MealOptionsService) {}

  @Get('category/:categoryId')
  findByCategoryId(
    @Param('categoryId') categoryId: string,
  ): Promise<MealOptionEntity[]> {
    return this.mealOptionsService.findByCategoryId(categoryId);
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<MealOptionEntity> {
    return this.mealOptionsService.findOne(id);
  }

  @Post('category/:categoryId')
  create(
    @Param('categoryId') categoryId: string,
    @Body() dto: CreateMealOptionDto,
  ): Promise<MealOptionEntity> {
    return this.mealOptionsService.create(categoryId, dto);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() dto: UpdateMealOptionDto,
  ): Promise<MealOptionEntity> {
    return this.mealOptionsService.update(id, dto);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.mealOptionsService.remove(id);
  }
}

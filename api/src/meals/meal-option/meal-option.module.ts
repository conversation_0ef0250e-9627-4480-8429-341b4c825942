import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MealOptionsController } from './meal-option.controller';
import { JwtService } from '@nestjs/jwt';
import { MealOptionsService } from './meal-option.service';
import { MealOptionEntity } from 'src/models/meals';
import { AccessTokenEntity } from 'src/models/user-entity';

@Module({
  imports: [TypeOrmModule.forFeature([MealOptionEntity, AccessTokenEntity])],
  controllers: [MealOptionsController],
  providers: [MealOptionsService, JwtService],
})
export class MealOptionModule {}

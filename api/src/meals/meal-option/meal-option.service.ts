import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MealCategoryEntity, MealOptionEntity } from 'src/models/meals';
import { Repository } from 'typeorm';
import { CreateMealOptionDto, UpdateMealOptionDto } from './dto';

@Injectable()
export class MealOptionsService {
  constructor(
    @InjectRepository(MealOptionEntity)
    private mealOptionsRepository: Repository<MealOptionEntity>,
  ) {}

  async findByCategoryId(categoryId: string): Promise<MealOptionEntity[]> {
    return this.mealOptionsRepository.find({
      where: { category: { id: categoryId } },
      relations: ['food'],
    });
  }

  async findOne(id: string): Promise<MealOptionEntity> {
    return this.mealOptionsRepository.findOne({
      where: { id },
      relations: ['food', 'category'],
    });
  }

  async create(
    categoryId: string,
    dto: CreateMealOptionDto,
  ): Promise<MealOptionEntity> {
    const category = new MealCategoryEntity();
    category.id = categoryId;

    const newOption = this.mealOptionsRepository.create({
      ...dto,
      category,
    });

    return this.mealOptionsRepository.save(newOption);
  }

  async update(
    id: string,
    dto: UpdateMealOptionDto,
  ): Promise<MealOptionEntity> {
    await this.mealOptionsRepository.update(id, dto);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    await this.mealOptionsRepository.delete(id);
  }
}

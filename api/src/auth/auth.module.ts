import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AccessTokenModule } from './access-token/access-token.module';
import { OtpModule } from './otp/otp.module';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { JwtStrategy } from 'src/security/middleware/jwt.strategy';
import { FirebaseService } from 'src/third-party/firebase/firebase-authentication.service';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserEntity, RoleEntity, AccessTokenEntity]),
    AccessTokenModule,
    OtpModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, FirebaseService, JwtStrategy],
  exports: [AuthService, JwtStrategy],
})
export class AuthModule {}

import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/utils/responses';

export class LoginResDto extends BaseResponse {
  @ApiProperty({
    example: 'logged in successfully !!',
    description: 'success message',
  })
  msg: string;

  @ApiProperty({
    example: 'randomvalue',
    description: 'Access token for authentication and authropization',
  })
  token: string;

  @ApiProperty({
    example: 'timestamp',
    description: 'token expiry',
  })
  expiry: number;
}

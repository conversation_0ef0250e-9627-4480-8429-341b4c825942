import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OtpController } from './otp.controller';
import { SesService } from 'src/third-party/aws/SES/ses.service';
import { OtpService } from './otp.service';
import { FirebaseService } from 'src/third-party/firebase/firebase-authentication.service';
import { OTPEntity, UserEntity } from 'src/models/user-entity';

@Module({
  imports: [TypeOrmModule.forFeature([OTPEntity, UserEntity])],
  controllers: [OtpController],
  providers: [OtpService, SesService, FirebaseService],
})
export class OtpModule {}

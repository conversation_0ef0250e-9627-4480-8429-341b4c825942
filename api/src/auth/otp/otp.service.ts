import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { OTPEntity, OTPEnum, UserEntity } from 'src/models/user-entity';
import { SesService } from 'src/third-party/aws/SES/ses.service';
import { FirebaseService } from 'src/third-party/firebase/firebase-authentication.service';
import { Repository } from 'typeorm';
import { ForgotPasswordDto, ResetPasswordDto, VerifyOtpDto } from './dto';

@Injectable()
export class OtpService {
  constructor(
    @InjectRepository(OTPEntity)
    private otpRepo: Repository<OTPEntity>,

    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,

    private sesService: SesService,
    private configService: ConfigService,
    private firebaseService: FirebaseService,
  ) {}

  // Helper method to hash password
  async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    try {
      const { email } = forgotPasswordDto;

      // Check if user exists
      const user = await this.userRepo.findOne({ where: { email } });
      if (!user) {
        throw new NotFoundException('No account found with this email address');
      }

      // Generate a 6-digit OTP - fixed the randomInt usage
      const otp = Math.floor(100000 + Math.random() * 900000).toString();

      console.log({ otp });

      // Calculate expiry (15 minutes from now)
      const expiry = new Date();
      expiry.setMinutes(expiry.getMinutes() + 15);

      // First check if there's an existing OTP for this email and type
      const existingOtp = await this.otpRepo.findOne({
        where: {
          email,
          otp_type: OTPEnum.FORGOTPASS,
          isDeleted: false,
        },
      });

      if (existingOtp) {
        // Update existing OTP
        existingOtp.OTP = otp;
        existingOtp.expiry = expiry;
        await this.otpRepo.save(existingOtp);
      } else {
        // Create new OTP record
        const otpEntity = this.otpRepo.create({
          email,
          OTP: otp,
          otp_type: OTPEnum.FORGOTPASS,
          expiry,
          isDeleted: false,
        });

        await this.otpRepo.save(otpEntity);
      }

      // Get the from email address with validation
      const from = this.configService.get<string>('AWS_SES_EMAIL');
      if (!from) {
        console.error('EMAIL_FROM_ADDRESS is not configured');
        throw new Error('Email configuration error');
      }

      const subject = 'Reset Your Password';
      const body = `Your password reset code is: ${otp}. This code will expire in 15 minutes.`;
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Reset Your Password</h2>
          <p>We received a request to reset your password. Use the code below to complete the process:</p>
          <div style="background-color: #f4f4f4; padding: 15px; text-align: center; font-size: 24px; letter-spacing: 5px; margin: 20px 0;">
            <strong>${otp}</strong>
          </div>
          <p>This code will expire in 15 minutes.</p>
          <p>If you didn't request this, you can safely ignore this email.</p>
          <p>Thanks,<br>The Team</p>
        </div>
      `;

      await this.sesService.sendEmail(email, from, subject, body, html);

      return {
        message: 'Password reset OTP sent to your email',
        email,
      };
    } catch (error) {
      console.error(`Forgot Password Error: ${error.message}`);

      // For security, return same message even if email doesn't exist
      if (error instanceof NotFoundException) {
        return {
          message:
            'If an account with this email exists, a password reset OTP has been sent',
        };
      }

      throw new BadRequestException('Failed to process password reset request');
    }
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto) {
    try {
      const { email, otp, otp_type } = verifyOtpDto;

      // Validate OTP
      const otpEntity = await this.otpRepo.findOne({
        where: {
          email,
          OTP: otp,
          otp_type,
          isDeleted: false,
        },
      });

      if (!otpEntity) {
        throw new BadRequestException('Invalid OTP');
      }

      // Check if OTP is expired
      if (otpEntity.expiry < new Date()) {
        // Mark expired OTP as deleted
        otpEntity.isDeleted = true;
        await this.otpRepo.save(otpEntity);
        throw new BadRequestException('OTP has expired');
      }

      return {
        message: 'OTP verified successfully',
        email,
      };
    } catch (error) {
      console.error(`OTP Verification Error: ${error.message}`);
      throw new BadRequestException(error.message);
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    try {
      const { email, otp, newPassword } = resetPasswordDto;

      // First verify the OTP
      await this.verifyOtp({
        email,
        otp,
        otp_type: OTPEnum.FORGOTPASS,
      });

      // Find user
      const user = await this.userRepo.findOne({ where: { email } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Hash the new password
      const passwordHash = await this.hashPassword(newPassword);

      // Update password in our database
      user.passwordHash = passwordHash;
      await this.userRepo.save(user);

      // Update password in Firebase
      await this.firebaseService.updateUserPassword(user.id, newPassword);

      // Mark OTP as used (deleted)
      const otpEntity = await this.otpRepo.findOne({
        where: {
          email,
          OTP: otp,
          otp_type: OTPEnum.FORGOTPASS,
          isDeleted: false,
        },
      });

      if (otpEntity) {
        otpEntity.isDeleted = true;
        await this.otpRepo.save(otpEntity);
      }

      // Send confirmation email
      const from = this.configService.get<string>('AWS_SES_EMAIL');
      const subject = 'Password Reset Successful';
      const body = 'Your password has been reset successfully.';
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Password Reset Successful</h2>
          <p>Your password has been reset successfully.</p>
          <p>If you didn't make this change or if you believe an unauthorized person has accessed your account, please contact us immediately.</p>
          <p>Thanks,<br>The Team</p>
        </div>
      `;

      await this.sesService.sendEmail(email, from, subject, body, html);

      return {
        message: 'Password has been reset successfully',
      };
    } catch (error) {
      console.error(`Reset Password Error: ${error.message}`);
      throw new BadRequestException(error.message);
    }
  }
}

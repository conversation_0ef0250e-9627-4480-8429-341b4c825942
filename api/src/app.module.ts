import config from 'src/common/configuration/properties';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AccessTokenEntity,
  PermissionEntity,
  RoleEntity,
  UserEntity,
} from './models/user-entity';
import { FoodItemEntity } from './models/meals';
import { ExerciseEntity } from './models/excercise';
import { CommonModule } from './common/common.module';
import { ThirdPartyModule } from './third-party/third-party.module';
import { SecurityModule } from './security/security.module';
import { AuthModule } from './auth/auth.module';
import { AdminModule } from './admin/admin.module';
import { TraineeModule } from './trainee/trainee.module';
import { MealsModule } from './meals/meals.module';
import { ExercisesModule } from './exercises/exercises.module';
import { TwilioModule } from './twilio/twilio.module';
import { BootstrapService } from './bootstrap.service';
import { NetworkService } from './network.service';
import { APIUrlLoggerMiddleware } from './security/middleware/ApiUrlLogger.middleware';
import { MulterModule } from '@nestjs/platform-express';

@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRoot(config[process.env.NODE_ENV]()['ormConfig']),
    TypeOrmModule.forFeature([
      UserEntity,
      PermissionEntity,
      RoleEntity,
      AccessTokenEntity,
      FoodItemEntity,
      ExerciseEntity,
    ]),
    MulterModule.register({}),

    CommonModule,
    ThirdPartyModule,
    SecurityModule,
    AuthModule,

    AdminModule,
    TraineeModule,
    MealsModule,
    ExercisesModule,

    TwilioModule,
  ],
  controllers: [],
  providers: [BootstrapService, NetworkService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(APIUrlLoggerMiddleware).forRoutes('*');
  }
}

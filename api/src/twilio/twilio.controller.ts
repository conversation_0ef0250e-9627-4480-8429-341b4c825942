import {
  <PERSON>,
  Post,
  Get,
  Body,
  Headers,
  Req,
  Logger,
  HttpStatus,
  HttpException,
  Res,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { TwilioService } from './twilio.service';
import { MessageObserverService } from './message-observer.service';
import { TwilioResponseDto, TwilioWebhookDto } from './dto';
import { Request, Response } from 'express';

@ApiTags('twilio')
@Controller('twilio')
export class TwilioController {
  private readonly logger = new Logger(TwilioController.name);

  constructor(
    private readonly twilioService: TwilioService,
    private readonly messageObserverService: MessageObserverService,
  ) {}

  @Post('webhook')
  @ApiOperation({ summary: 'Webhook for Twilio WhatsApp messages' })
  @ApiResponse({
    status: 200,
    description: 'Message processed successfully with TwiML response',
  })
  async handleWhatsAppWebhook(
    @Body() body: TwilioWebhookDto,
    @Headers('x-twilio-signature') signature: string,
    @Req() request: Request,
    @Res() response: Response,
  ) {
    try {
      this.logger.log(`Received WhatsApp webhook: ${JSON.stringify(body)}`);
      this.logger.log(`Content-Type: ${request.headers['content-type']}`);
      this.logger.log(
        `Request URL: ${request.protocol}://${request.get('host')}${request.originalUrl}`,
      );
      this.logger.log(`Request Method: ${request.method}`);
      this.logger.log(`All Headers: ${JSON.stringify(request.headers)}`);

      // Log the entire payload for debugging
      this.logger.log(`Full Twilio webhook payload: ${JSON.stringify(body)}`);

      // Extract message details from Twilio webhook payload
      // Twilio sends form data with these exact field names
      const from = body.From;
      const messageBody = body.Body;
      const to = body.To;
      const messageSid = body.MessageSid || body.SmsSid;
      const profileName = body.ProfileName;
      const waId = body.WaId;

      this.logger.log(`Extracted fields:`);
      this.logger.log(`- From: ${from}`);
      this.logger.log(`- To: ${to}`);
      this.logger.log(`- Body: ${messageBody}`);
      this.logger.log(`- MessageSid: ${messageSid}`);
      this.logger.log(`- ProfileName: ${profileName}`);
      this.logger.log(`- WaId: ${waId}`);

      // Check for required fields
      if (!from || !messageBody) {
        this.logger.error(
          `Missing required fields in Twilio webhook: ${JSON.stringify(body)}`,
        );

        // Try to extract from other possible field names
        const extractedFrom = from || body.WaId || body.sender || body.Sender;
        const extractedBody =
          messageBody || body.message || body.Message || body.text || body.Text;

        if (!extractedFrom || !extractedBody) {
          // If still missing required fields, return error response
          const errorTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Message>Sorry, I couldn't process your message. Required information is missing.</Message>
</Response>`;

          response.header('Content-Type', 'text/xml; charset=utf-8');
          response.send(errorTwiml);
          return;
        }

        // Use extracted fields
        this.logger.log(
          `Using extracted fields: From=${extractedFrom}, Body=${extractedBody}`,
        );

        // Process the message with extracted fields
        const aiResponse = await this.twilioService.processWhatsAppMessage(
          extractedFrom,
          extractedBody,
        );

        // Use the same approach as the main handler - try direct send first, then TwiML as backup
        let directSendSuccessful = false;

        // First try to send the message directly via the Twilio API
        try {
          this.logger.log(
            `Sending response directly to ${extractedFrom} via Twilio API (extracted fields)`,
          );
          await this.twilioService.sendWhatsAppMessage(
            extractedFrom,
            aiResponse,
          );
          this.logger.log(
            `Successfully sent direct message to ${extractedFrom} (extracted fields)`,
          );
          directSendSuccessful = true;
        } catch (sendError) {
          this.logger.error(
            `Failed to send direct message (extracted fields): ${sendError.message}`,
          );
          // We'll fall back to TwiML response
        }

        // Always return a TwiML response to Twilio
        const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  ${directSendSuccessful ? '<!-- Message already sent directly -->' : `<Message>${aiResponse}</Message>`}
</Response>`;

        // Log the TwiML response for debugging
        this.logger.log(
          `Sending TwiML response (extracted fields) to Twilio: ${twiml.substring(0, 100)}${twiml.length > 100 ? '...' : ''}`,
        );

        // Set proper content type with UTF-8 encoding for non-English characters
        response.header('Content-Type', 'text/xml; charset=utf-8');
        response.send(twiml);

        return;
      }

      // Process the message
      const aiResponse = await this.twilioService.processWhatsAppMessage(
        from,
        messageBody,
      );

      let directSendSuccessful = false;

      // First try to send the message directly via the Twilio API
      try {
        this.logger.log(`Sending response directly to ${from} via Twilio API`);
        await this.twilioService.sendWhatsAppMessage(from, aiResponse);
        this.logger.log(`Successfully sent direct message to ${from}`);
        directSendSuccessful = true;
      } catch (sendError) {
        this.logger.error(
          `Failed to send direct message: ${sendError.message}`,
        );
        // We'll fall back to TwiML response
      }

      const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  ${directSendSuccessful ? '<!-- Message already sent directly -->' : `<Message>${aiResponse}</Message>`}
</Response>`;

      // Log the TwiML response for debugging
      this.logger.log(
        `Sending TwiML response to Twilio: ${twiml.substring(0, 100)}${twiml.length > 100 ? '...' : ''}`,
      );

      // Set proper content type with UTF-8 encoding for non-English characters
      response.header('Content-Type', 'text/xml; charset=utf-8');
      response.send(twiml);
    } catch (error) {
      this.logger.error(`Error handling WhatsApp webhook: ${error.message}`);

      const errorTwiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Message>Sorry, I encountered an error processing your message. Please try again later.</Message>
</Response>`;

      this.logger.log(`Sending error TwiML response to Twilio: ${errorTwiml}`);

      // Set proper content type with UTF-8 encoding
      response.header('Content-Type', 'text/xml; charset=utf-8');
      response.send(errorTwiml);

      // Try direct send for error message too
      try {
        const from = body?.From || 'unknown';
        if (from !== 'unknown') {
          this.logger.log(
            `Attempting to send error message directly to ${from}`,
          );
          await this.twilioService.sendWhatsAppMessage(
            from,
            'Sorry, I encountered an error processing your message. Please try again later.',
          );
          this.logger.log(`Successfully sent direct error message to ${from}`);
        }
      } catch (sendError) {
        this.logger.error(
          `Failed to send direct error message: ${sendError.message}`,
        );
      }
    }
  }

  @Post('send')
  @ApiOperation({ summary: 'Send a WhatsApp message' })
  @ApiResponse({
    status: 200,
    description: 'Message sent successfully',
    type: TwilioResponseDto,
  })
  async sendWhatsAppMessage(@Body() body: any) {
    try {
      // Validate required fields with detailed logging
      if (!body) {
        this.logger.error('Request body is empty or undefined');
        throw new HttpException(
          'Request body is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.log(
        `Received send request with body: ${JSON.stringify(body)}`,
      );

      // Handle both standard format and Twilio webhook format
      let to: string;
      let message: string;

      // Check if this is a Twilio webhook format (has From, Body fields)
      if (body.From && body.Body) {
        this.logger.log('Detected Twilio webhook format, extracting fields');
        to = body.From; // The sender becomes the recipient for the reply
        message = body.Body;

        // Process the message through the AI
        this.logger.log(`Processing message through AI: ${message}`);
        try {
          const aiResponse = await this.twilioService.processWhatsAppMessage(
            to,
            message,
          );
          message = aiResponse; // Use the AI response as the message to send back
        } catch (aiError) {
          this.logger.error(
            `Error processing message through AI: ${aiError.message}`,
          );
          message =
            "Sorry, I couldn't process your message. Please try again later.";
        }
      } else {
        // Standard format with to/message fields
        to = body.to;
        message = body.message;

        if (!to) {
          this.logger.error('Missing "to" field in request');
          throw new HttpException(
            'The "to" field is required',
            HttpStatus.BAD_REQUEST,
          );
        }

        if (!message) {
          this.logger.error('Missing "message" field in request');
          throw new HttpException(
            'The "message" field is required',
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      // Format the phone number if needed
      let formattedTo = to;

      // If it doesn't have the whatsapp: prefix and doesn't start with +, add the + prefix
      if (
        !formattedTo.startsWith('whatsapp:') &&
        !formattedTo.startsWith('+')
      ) {
        formattedTo = '+' + formattedTo;
        this.logger.log(`Formatted phone number to: ${formattedTo}`);
      }

      // Log the request
      this.logger.log(
        `Sending WhatsApp message to ${formattedTo}: ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`,
      );

      // Send the message
      await this.twilioService.sendWhatsAppMessage(formattedTo, message);

      return {
        success: true,
        message: `Message sent to ${formattedTo}`,
      };
    } catch (error) {
      this.logger.error(`Error sending WhatsApp message: ${error.message}`);

      // Return a more detailed error response
      throw new HttpException(
        error.message || 'Failed to send WhatsApp message',
        error instanceof HttpException
          ? error.getStatus()
          : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('direct-reply')
  @ApiOperation({
    summary: 'Process a message and send a direct reply (bypassing TwiML)',
  })
  @ApiResponse({ status: 200, description: 'Reply sent directly' })
  async directReply(@Body() body: any) {
    try {
      if (!body || !body.From || !body.Body) {
        throw new HttpException(
          'Missing required fields: From and Body',
          HttpStatus.BAD_REQUEST,
        );
      }

      this.logger.log(
        `Processing direct reply for message from ${body.From}: ${body.Body}`,
      );

      // Process the message through the AI
      const aiResponse = await this.twilioService.processWhatsAppMessage(
        body.From,
        body.Body,
      );

      // Send the response directly back to the sender
      const sender = body.From;

      this.logger.log(
        `Sending direct reply to ${sender}: ${aiResponse.substring(0, 50)}${aiResponse.length > 50 ? '...' : ''}`,
      );

      // Send the message directly
      await this.twilioService.sendWhatsAppMessage(sender, aiResponse);

      return {
        success: true,
        message: `Reply sent to ${sender}`,
        originalMessage: body.Body,
        aiResponse: aiResponse,
      };
    } catch (error) {
      this.logger.error(`Error sending direct reply: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to send direct reply',
        error instanceof HttpException
          ? error.getStatus()
          : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('typing-indicators')
  @ApiOperation({ summary: 'Get status of active typing indicators' })
  @ApiResponse({
    status: 200,
    description: 'Active typing indicators retrieved successfully',
  })
  async getActiveTypingIndicators() {
    try {
      const activeProcessing =
        this.messageObserverService.getActiveProcessing();

      // Convert the Map to an array of objects for the response
      const activeIndicators = Array.from(activeProcessing.entries()).map(
        ([phoneNumber, info]) => {
          return {
            phoneNumber,
            startTime: new Date(info.startTime).toISOString(),
            lastTypingTime: new Date(info.lastTypingTime).toISOString(),
            elapsedSeconds: Math.floor((Date.now() - info.startTime) / 1000),
          };
        },
      );

      return {
        success: true,
        activeCount: activeIndicators.length,
        activeIndicators,
      };
    } catch (error) {
      this.logger.error(
        `Error getting active typing indicators: ${error.message}`,
      );
      throw new HttpException(
        'Failed to get active typing indicators',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

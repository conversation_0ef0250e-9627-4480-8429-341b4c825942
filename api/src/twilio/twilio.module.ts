import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgentsModule } from 'src/agents/agents.module';
import { AuthModule } from 'src/auth/auth.module';
import { FoodItemsModule } from 'src/meals/food-items/food-items.module';
import { TrainingPlanEntity } from 'src/models/excercise';
import { FoodItemEntity, MealEntity } from 'src/models/meals';
import { TraineeProfileEntity } from 'src/models/profiles';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { TwilioController } from './twilio.controller';
import { MessageObserverService } from './message-observer.service';
import { TwilioService } from './twilio.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TraineeProfileEntity,
      TrainingPlanEntity,
      MealEntity,
      FoodItemEntity,
    ]),
    HttpModule,
    ConfigModule,
    AgentsModule,
    AuthModule,
    FoodItemsModule,
    ThirdPartyModule,
  ],
  controllers: [TwilioController],
  providers: [TwilioService, MessageObserverService],
  exports: [TwilioService, MessageObserverService],
})
export class TwilioModule {}

import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AgentsService } from 'src/agents/agents.service';
import { TrainingPlanEntity } from 'src/models/excercise';
import { FoodItemEntity, MealEntity } from 'src/models/meals';
import { TraineeProfileEntity } from 'src/models/profiles';
import { Repository } from 'typeorm';
import { MessageObserverService } from './message-observer.service';
import { AuthService } from 'src/auth/auth.service';
import { FoodItemsService } from 'src/meals/food-items/food-items.service';
import { TwilioClientService } from 'src/third-party/twilio/twilio-client.service';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class TwilioService {
  private readonly logger = new Logger(TwilioService.name);

  private readonly flaskApiUrl: string;

  constructor(
    @InjectRepository(TraineeProfileEntity)
    private readonly traineeProfileRepository: Repository<TraineeProfileEntity>,

    @InjectRepository(TrainingPlanEntity)
    private readonly trainingPlanRepository: Repository<TrainingPlanEntity>,

    @InjectRepository(MealEntity)
    private readonly mealRepository: Repository<MealEntity>,

    private readonly agentsService: AgentsService,
    private readonly messageObserverService: MessageObserverService,
    private readonly authService: AuthService,
    private readonly foodItemsService: FoodItemsService,
    private readonly twilioClientService: TwilioClientService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.flaskApiUrl =
      this.configService.get<string>('FLASK_API_URL') ||
      'http://localhost:5000';
    this.logger.log(`Flask API URL: ${this.flaskApiUrl}`);
  }

  // Process incoming WhatsApp message
  async processWhatsAppMessage(from: string, body: string): Promise<string> {
    try {
      this.logger.log(`Processing WhatsApp message from ${from}: ${body}`);

      // Validate inputs
      if (!from) {
        this.logger.error('From parameter is missing or empty');
        throw new Error('From parameter is required');
      }

      if (!body) {
        this.logger.error('Body parameter is missing or empty');
        throw new Error('Body parameter is required');
      }

      // Extract user ID from WhatsApp number
      // First, normalize the 'from' parameter
      let normalizedFrom = from;

      // Log the original format
      this.logger.log(`Original 'from' format: ${from}`);

      // Try to find user by mobile number
      const user = await this.authService.findByMobileNumber(normalizedFrom);

      // Set userId based on user lookup result
      let userId: string;
      let userName: string | null = null;
      let traineeProfile = null;
      let trainingPlans = [];
      let mealPlans = [];
      let meals = [];

      if (user) {
        // If user found, use their ID
        userId = user.id;
        userName = user.name;

        // Get trainee profile information if available
        traineeProfile = await this.getTraineeProfileByUserId(user.id);

        // Get training plans if available
        trainingPlans = await this.getTrainingPlansByUserId(user.id);

        // Get meal plans if available
        mealPlans = await this.getMealsByUserId(user.id);

        // Get meals from meals table if available
        meals = await this.getMealsByUserId(user.id);

        this.logger.log(
          `Found user with ID: ${userId}, name: ${userName} for mobile number: ${normalizedFrom}`,
        );
      } else {
        // If no user found, extract number as userId (fallback)
        userId = normalizedFrom.replace('whatsapp:', '').replace(/\+/g, '');

        // Ensure we have a valid user ID - use a default prefix if needed
        if (!userId || userId.length < 3) {
          const randomId = Math.floor(Math.random() * 10000);
          userId = `whatsapp_user_${randomId}`;
          this.logger.log(
            `Generated random user ID: ${userId} (original was invalid: '${normalizedFrom}')`,
          );
        } else {
          this.logger.log(
            `No user found. Using extracted ID: ${userId} from ${normalizedFrom}`,
          );
        }
      }

      // Start the typing indicator observer
      // This will send "מקליד..." every 15 seconds until we get a response
      const stopTypingIndicator =
        this.messageObserverService.startObserving(from);
      this.logger.log(`Started typing indicator for ${from}`);

      try {
        // Process message through agents service
        this.logger.log(
          `Sending message to agents service with userId: ${userId}, body: ${body}`,
        );

        // Prepare message with user context if available
        let messageWithContext = body;
        if (userName) {
          // Add user context to the beginning of the message
          // This will be processed by the AI agent to personalize responses
          this.logger.log(`Adding user context: User name is ${userName}`);

          // Build context string with user information
          let contextString = `[User: ${userName}`;

          // Add trainee profile info if available
          if (traineeProfile) {
            contextString += `, Age: ${traineeProfile.age}, Goal: ${traineeProfile.fitnessGoal}`;
          }

          // Add training plan info if available
          if (trainingPlans && trainingPlans.length > 0) {
            contextString += `, Has training plans: ${trainingPlans.length}`;
          }

          // Add meal plan info if available
          if (mealPlans && mealPlans.length > 0) {
            contextString += `, Has meal plans: ${mealPlans.length}`;
          }

          // Add meals info if available
          if (meals && meals.length > 0) {
            contextString += `, Has meals: ${meals.length}`;

            // Add detailed meal information to the context
            contextString += `\n\nMeal Plan Details:`;
            meals.forEach((meal, index) => {
              contextString += `\nMeal ${index + 1}: ${meal.name}`;
              if (meal.description) {
                contextString += ` - ${meal.description}`;
              }

              // Add food items
              if (meal.categories && meal.categories.length > 0) {
                contextString += `\n  Categories:`;
                meal.categories.forEach((category) => {
                  contextString += `\n    ${category.name}:`;
                  if (category.options && category.options.length > 0) {
                    category.options.forEach((option) => {
                      const foodName = option.food
                        ? option.food.name
                        : 'Unknown food';
                      contextString += `\n      ${foodName} - Amount: ${option.amount}`;
                    });
                  }
                });
              }

              // Add macro information if available
              if (meal.macroRange) {
                contextString += `\n  Macros: Protein: ${meal.macroRange.min.protein}g-${meal.macroRange.max.protein}g, Carbs: ${meal.macroRange.min.carbs}g-${meal.macroRange.max.carbs}g, Fats: ${meal.macroRange.min.fats}g-${meal.macroRange.max.fats}g, Calories: ${meal.macroRange.min.calories}-${meal.macroRange.max.calories}`;
              }
            });
          }

          contextString += '] ';
          messageWithContext = contextString + body;

          this.logger.log(`Enhanced context: ${contextString}`);
        }

        // Update Redis with user data from the API
        try {
          // Prepare user data for Redis
          const userData = {
            user: {
              id: userId,
              name: userName || '',
            },
            traineeProfile: traineeProfile || {},
            mealPlan: meals || [],
            trainingPlan: trainingPlans || [],
          };

          // Call the API to update Redis with user data
          await this.httpService
            .post(
              `${this.flaskApiUrl}/api/user/update-from-api?user_id=${userId}&channel=whatsapp`,
              userData,
            )
            .toPromise();

          this.logger.log(`Updated Redis with user data for ${userId}`);
        } catch (updateError) {
          this.logger.error(
            `Failed to update Redis with user data: ${updateError.message}`,
          );
          // Continue with the message processing even if the update fails
        }

        // Call the agents service with WhatsApp channel
        // First, modify the API call to include the channel parameter
        // We need to add a custom parameter to the API call
        const response = await this.agentsService.sendChatMessage(
          userId,
          messageWithContext,
          'whatsapp', // Specify the channel as WhatsApp
        );

        // Stop the typing indicator observer
        stopTypingIndicator();
        this.logger.log(`Stopped typing indicator for ${from}`);

        // Validate the response
        if (!response) {
          this.logger.error(
            `Empty response object from agents service for user ${userId}`,
          );
          throw new Error('Empty response from agents service');
        }

        if (!response.response) {
          this.logger.error(
            `Response object missing 'response' field: ${JSON.stringify(response)}`,
          );
          throw new Error('Invalid response format from agents service');
        }

        this.logger.log(
          `Response for ${userId}: ${response.response.substring(0, 100)}${response.response.length > 100 ? '...' : ''}`,
        );

        // We'll let the controller handle sending the message
        // This prevents duplicate messages
        this.logger.log(
          `Skipping direct message send from service to avoid duplicates`,
        );

        // The controller will handle sending the message via TwiML or direct API call

        return response.response; // Return the AI response
      } catch (error) {
        // Stop the typing indicator observer in case of error
        stopTypingIndicator();
        this.logger.log(`Stopped typing indicator for ${from} due to error`);

        // If there's an error with the specific user ID, try with a default test user
        this.logger.warn(
          `Error with user ID ${userId}, trying with default test user: ${error.message}`,
        );

        try {
          this.logger.log(
            `Falling back to default test user for message processing`,
          );

          // Add a note about using a default user
          const fallbackMessage = `[Default test user] ${body}`;

          const response = await this.agentsService.sendChatMessage(
            'test-user-123',
            fallbackMessage,
            'whatsapp', // Specify the channel as WhatsApp for fallback as well
          );

          if (!response || !response.response) {
            throw new Error(
              'Empty response from agents service with default user',
            );
          }

          // Skip direct send from service to avoid duplicates
          this.logger.log(
            `Skipping direct message send from fallback service to avoid duplicates`,
          );
          // The controller will handle sending the message

          return response.response;
        } catch (fallbackError) {
          this.logger.error(
            `Fallback to default user also failed: ${fallbackError.message}`,
          );
          throw fallbackError; // Re-throw to be caught by the outer catch block
        }
      }
    } catch (error) {
      this.logger.error(`Error processing WhatsApp message: ${error.message}`);

      // Make sure typing indicator is stopped in case of outer error
      try {
        // We need to check if the observer is still active for this phone number
        this.messageObserverService.stopObserving(from);
        this.logger.log(
          `Ensured typing indicator is stopped for ${from} in error handler`,
        );
      } catch (observerError) {
        this.logger.error(
          `Error stopping typing indicator: ${observerError.message}`,
        );
      }

      // Check if the Flask API is available
      try {
        const healthStatus = await this.agentsService.checkHealth();
        this.logger.log(
          `Flask API health status: ${JSON.stringify(healthStatus)}`,
        );
      } catch (healthError) {
        this.logger.error(
          `Flask API health check failed: ${healthError.message}`,
        );
      }

      const errorMessage =
        'Sorry, I encountered an error processing your message. Please try again later.';

      // Try to send error message directly
      try {
        await this.sendWhatsAppMessage(from, errorMessage);
        this.logger.log(`Sent error message directly to ${from}`);
      } catch (sendError) {
        this.logger.error(
          `Failed to send error message directly: ${sendError.message}`,
        );
      }

      return errorMessage;
    }
  }

  // Send WhatsApp message
  async sendWhatsAppMessage(to: string, message: string): Promise<void> {
    try {
      // Use the TwilioClientService to send the message
      await this.twilioClientService.sendWhatsAppMessage(to, message);
    } catch (error) {
      this.logger.error(`Error sending WhatsApp message: ${error.message}`);
      throw error;
    }
  }

  // Verify Twilio request signature (optional but recommended for security)
  verifyTwilioSignature(signature: string, url: string, params: any): boolean {
    try {
      return this.twilioClientService.verifyTwilioSignature(
        signature,
        url,
        params,
      );
    } catch (error) {
      this.logger.error(`Error verifying Twilio signature: ${error.message}`);
      return false;
    }
  }

  async getTraineeProfileByUserId(userId: string): Promise<any> {
    try {
      this.logger.log(`Getting trainee profile for user ID: ${userId}`);

      // Find trainee profile by user ID
      const traineeProfile = await this.traineeProfileRepository.findOne({
        where: { user: { id: userId } },
      });

      if (traineeProfile) {
        this.logger.log(`Found trainee profile for user ID: ${userId}`);

        // Return trainee profile information
        return {
          id: traineeProfile.id,
          age: traineeProfile.age,
          gender: traineeProfile.gender,
          height: traineeProfile.height,
          weight: traineeProfile.weight,
          bodyFatPercentage: traineeProfile.bodyFatPercentage,
          fitnessGoal: traineeProfile.fitnessGoal,
          activityLevel: traineeProfile.activityLevel,
          profileImageUrl: traineeProfile.profileImageUrl,
        };
      } else {
        this.logger.log(`No trainee profile found for user ID: ${userId}`);
        return null;
      }
    } catch (error) {
      console.error(`Error getting trainee profile: ${error.message}`);
      this.logger.error(`Error getting trainee profile: ${error.message}`);
      return null;
    }
  }

  /**
   * Get training plans for a user
   * @param userId The user ID
   * @returns The training plans or empty array if none found
   */
  async getTrainingPlansByUserId(userId: string): Promise<any[]> {
    try {
      this.logger.log(`Getting training plans for user ID: ${userId}`);

      // Find training plans by user ID
      const trainingPlans = await this.trainingPlanRepository.find({
        where: { user: { id: userId } },
        relations: ['exercises', 'exercises.exercise'],
      });

      if (trainingPlans && trainingPlans.length > 0) {
        this.logger.log(
          `Found ${trainingPlans.length} training plans for user ID: ${userId}`,
        );

        // Return simplified training plans
        return trainingPlans.map((plan) => ({
          id: plan.id,
          day: plan.day,
          focus: plan.focus,
          exercises: plan.exercises
            ? plan.exercises.map((ex) => ({
                id: ex.id,
                name: ex.name,
                sets: ex.sets,
                reps: ex.reps,
                rest: ex.rest,
                instructions: ex.instructions,
              }))
            : [],
        }));
      } else {
        this.logger.log(`No training plans found for user ID: ${userId}`);
        return [];
      }
    } catch (error) {
      console.error(`Error getting training plans: ${error.message}`);
      this.logger.error(`Error getting training plans: ${error.message}`);
      return [];
    }
  }

  async getMealsByUserId(userId: string): Promise<any[]> {
    try {
      this.logger.log(`Getting meals for user ID: ${userId}`);

      // Find meals by trainee ID
      const meals = await this.mealRepository.find({
        where: { trainee: { id: userId } },
        relations: [
          'categories',
          'categories.options',
          'categories.options.food',
        ],
      });

      if (meals && meals.length > 0) {
        // Collect all foodIds from meals to fetch them in one go
        const allFoodIds = new Set<string>();

        // Extract foodIds from meals
        for (const meal of meals) {
          if (meal.foodItemIds && meal.foodItemIds.length > 0) {
            for (const foodId of meal.foodItemIds) {
              allFoodIds.add(foodId);
            }
          }
        }

        // Fetch all food items at once if there are any foodItemIds
        const foodIds = Array.from(allFoodIds);
        let foodItems: FoodItemEntity[] = [];

        if (foodIds.length > 0) {
          foodItems = await this.foodItemsService.findByIds(foodIds);
        }

        // Create a map for quick lookup
        const foodItemsMap = new Map<string, FoodItemEntity>();
        for (const food of foodItems) {
          foodItemsMap.set(food.id, food);
        }

        meals.forEach((meal, index) => {
          if (meal.categories && meal.categories.length > 0) {
            meal.categories.forEach((category, catIndex) => {
              if (category.options && category.options.length > 0) {
                category.options.forEach((option, optIndex) => {
                  const foodName = option.food
                    ? option.food.name
                    : 'Unknown food';
                });
              }
            });
          }

          // Log foodItemIds if available
          if (meal.foodItemIds && meal.foodItemIds.length > 0) {
            meal.foodItemIds.forEach((foodId, foodIndex) => {
              const foodItem = foodItemsMap.get(foodId);
              const foodName = foodItem ? foodItem.name : 'Unknown food';
            });
          }
        });

        this.logger.log(`Found ${meals.length} meals for user ID: ${userId}`);

        // Return enhanced meals with food names
        return meals.map((meal) => ({
          id: meal.id,
          name: meal.name,
          description: meal.description,
          macroRange: meal.macroRange,
          categories: meal.categories
            ? meal.categories.map((category) => ({
                id: category.id,
                name: category.name,
                options: category.options
                  ? category.options.map((option) => ({
                      id: option.id,
                      amount: option.amount,
                      food: option.food
                        ? {
                            id: option.food.id,
                            name: option.food.name,
                          }
                        : null,
                    }))
                  : [],
              }))
            : [],
          foodItems: meal.foodItemIds
            ? meal.foodItemIds.map((foodId) => {
                const foodItem = foodItemsMap.get(foodId);
                return {
                  id: foodId,
                  name: foodItem ? foodItem.name : 'Unknown food',
                };
              })
            : [],
        }));
      } else {
        this.logger.log(`No meals found for user ID: ${userId}`);
        return [];
      }
    } catch (error) {
      console.error(`Error getting meals: ${error.message}`);
      this.logger.error(`Error getting meals: ${error.message}`);
      return [];
    }
  }
}

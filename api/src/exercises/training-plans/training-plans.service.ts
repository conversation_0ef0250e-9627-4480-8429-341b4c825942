import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ExerciseEntity,
  TrainingExerciseEntity,
  TrainingPlanEntity,
  TrainingPlanTemplateEntity,
} from 'src/models/excercise';
import { TrainerAssignmentEntity } from 'src/models/profiles';
import { ROLE_VALUES, RoleEntity, UserEntity } from 'src/models/user-entity';
import { Repository } from 'typeorm';
import { CreateTrainingPlanDto, UpdateTrainingPlanDto } from './dto';

@Injectable()
export class TrainingPlansService {
  constructor(
    @InjectRepository(TrainingPlanEntity)
    private readonly trainingPlanRepo: Repository<TrainingPlanEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepository: Repository<RoleEntity>,

    @InjectRepository(TrainerAssignmentEntity)
    private readonly trainerAssignmentsRepo: Repository<TrainerAssignmentEntity>,

    @InjectRepository(TrainingExerciseEntity)
    private readonly trainingExerciseRepo: Repository<TrainingExerciseEntity>,

    @InjectRepository(ExerciseEntity)
    private readonly exerciseRepo: Repository<ExerciseEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,

    @InjectRepository(TrainingPlanTemplateEntity)
    private readonly trainingPlanTemplateRepository: Repository<TrainingPlanTemplateEntity>,
  ) {}

  /**
   * Get all training plans (For Admins/Trainers)
   */
  async findAll(): Promise<TrainingPlanEntity[]> {
    return this.trainingPlanRepo.find({ relations: ['user', 'exercises'] });
  }

  /**
   * Get all training plans for a specific user
   */
  async findByUser(
    userId: string,
    requestingUser: UserEntity,
    roleValue,
  ): Promise<TrainingPlanEntity[]> {
    // Trainees can only view their own plans
    if (roleValue === ROLE_VALUES.TRAINEE && requestingUser.id !== userId) {
      throw new ForbiddenException(
        'Trainees can only view their own training plans.',
      );
    }

    return this.trainingPlanRepo.find({
      where: { user: { id: userId } },
      relations: ['exercises'],
    });
  }

  /**
   * Get a single training plan by ID
   */
  async findOne(
    id: string,
    requestingUser: UserEntity,
    roleValue,
  ): Promise<TrainingPlanEntity> {
    const plan = await this.trainingPlanRepo.findOne({
      where: { id },
      relations: ['user', 'exercises'],
    });

    if (!plan) throw new NotFoundException('Training plan not found');

    // Trainees can only access their own plans
    if (
      roleValue === ROLE_VALUES.TRAINEE &&
      requestingUser.id !== plan.user.id
    ) {
      throw new ForbiddenException('Access denied.');
    }

    return plan;
  }

  /**
   * Create a new training plan (Only Trainers)
   */
  async create(
    dto: CreateTrainingPlanDto,
    trainer: UserEntity,
    roleValue,
  ): Promise<TrainingPlanEntity> {
    if (roleValue !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can create training plans.');
    }

    // Check if the trainee is assigned to this trainer
    const trainerAssignment = await this.trainerAssignmentsRepo.findOne({
      where: {
        trainer: { id: trainer.id },
        trainee: { id: dto.traineeId },
        isActive: true,
        approved: true,
      },
    });

    if (!trainerAssignment) {
      throw new ForbiddenException(
        'Trainer is not assigned to this trainee or the assignment is not active/approved.',
      );
    }

    // Create the training plan
    const trainingPlan = this.trainingPlanRepo.create({
      ...dto,
      user: { id: dto.traineeId } as UserEntity, // Assign plan to the trainee
    });

    // Save the training plan first to get its ID
    const savedTrainingPlan = await this.trainingPlanRepo.save(trainingPlan);

    // Create training exercises
    const trainingExercises = dto.exercises.map((exercise) => ({
      sets: exercise.sets,
      reps: exercise.reps,
      rest: exercise.rest,
      instructions: exercise.instructions,
      name: exercise.name,
      exercise: { id: exercise.exerciseId },
      trainingPlan: savedTrainingPlan,
    }));

    // Save the training exercises
    await this.trainingExerciseRepo.save(trainingExercises);

    return savedTrainingPlan;
  }

  /**
   * Update a training plan (Only Trainers)
   */
  async update(
    id: string,
    updateDto: UpdateTrainingPlanDto,
    trainer: UserEntity,
    roleValue: string,
  ): Promise<TrainingPlanEntity> {
    // 1. Find the existing training plan with related user and exercises
    const plan = await this.trainingPlanRepo.findOne({
      where: { id },
      relations: ['user', 'exercises'],
    });

    if (!plan) {
      throw new NotFoundException('Training plan not found');
    }

    // 2. Verify the trainer is still assigned to this trainee
    const trainerAssignment = await this.trainerAssignmentsRepo.findOne({
      where: {
        trainer: { id: trainer.id },
        trainee: { id: plan.user.id },
        isActive: true,
        approved: true,
      },
    });

    if (!trainerAssignment) {
      throw new ForbiddenException(
        'Trainer is no longer assigned to this trainee or the assignment is not active/approved.',
      );
    }

    // 3. Update plan fields if provided
    if (updateDto.day !== undefined) plan.day = updateDto.day;
    if (updateDto.focus !== undefined) plan.focus = updateDto.focus;

    // 4. Handle exercises update
    if (updateDto.exercises) {
      // Remove old exercises
      await this.trainingExerciseRepo.delete({ trainingPlan: { id: plan.id } });

      // Add new exercises
      const newExercises: TrainingExerciseEntity[] = [];
      for (const ex of updateDto.exercises) {
        // Find the referenced exercise entity
        const exerciseEntity = await this.exerciseRepo.findOneBy({
          id: ex.exerciseId,
        });
        if (!exerciseEntity) {
          throw new NotFoundException(
            `Exercise with id ${ex.exerciseId} not found`,
          );
        }

        // Create new TrainingExerciseEntity
        const trainingExercise = this.trainingExerciseRepo.create({
          trainingPlan: plan,
          exercise: exerciseEntity,
          sets: ex.sets,
          reps: ex.reps,
          rest: ex.rest,
          instructions: ex.instructions,
          name: ex.name,
          videoUrl: ex.videoUrl,
        });
        newExercises.push(trainingExercise);
      }
      // Save all new exercises
      await this.trainingExerciseRepo.save(newExercises);

      // Attach to plan for return
      plan.exercises = newExercises;
    }

    // 5. Save updated plan
    const updatedPlan = await this.trainingPlanRepo.save(plan);

    // 6. Return the updated plan (with new exercises)
    return updatedPlan;
  }

  /**
   * Delete a training plan (Only Trainers)
   */
  async remove(id: string, trainer: UserEntity, roleValue): Promise<void> {
    const plan = await this.findOne(id, trainer, roleValue);

    if (roleValue !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can delete training plans.');
    }

    // Verify the trainer is still assigned to this trainee
    const trainerAssignment = await this.trainerAssignmentsRepo.findOne({
      where: {
        trainer: { id: trainer.id },
        trainee: { id: plan.user.id },
        isActive: true,
        approved: true,
      },
    });

    if (!trainerAssignment) {
      throw new ForbiddenException(
        'Trainer is no longer assigned to this trainee or the assignment is not active/approved.',
      );
    }

    const result = await this.trainingPlanRepo.delete(id);
    if (result.affected === 0)
      throw new NotFoundException('Training plan not found');
  }

  async getRoleById(roleId: number): Promise<RoleEntity> {
    return this.roleRepository.findOne({ where: { id: roleId } });
  }

  async createTrainingsFromTemplate(
    templateId: string,
    traineeId: string,
    trainerId: string,
  ) {
    if (!trainerId) {
      throw new BadRequestException('Please provide trainer Id.');
    }

    if (!traineeId) {
      throw new BadRequestException('Please provide trainee Id.');
    }

    const template = await this.trainingPlanTemplateRepository.findOneBy({
      id: templateId,
      trainerId,
    });

    if (!template) throw new NotFoundException('Template not found');

    const trainee = await this.userRepository.findOneBy({ id: traineeId });
    if (!trainee) throw new NotFoundException('Trainee not found');

    const createdTrainings = await Promise.all(
      template.templateData.map(async (planData) => {
        const exerciseIds = planData.exercises.map((ex) => ex.exerciseId);
        const exercises = await Promise.all(
          exerciseIds.map((id) => this.exerciseRepo.findOneBy({ id })),
        );

        const notFoundIds = exerciseIds.filter(
          (id, index) => !exercises[index],
        );

        if (notFoundIds.length > 0) {
          throw new NotFoundException(
            `Exercises not found: ${notFoundIds.join(', ')}`,
          );
        }

        // Step 1: Create the training plan
        const newTraining = this.trainingPlanRepo.create({
          day: planData.day,
          focus: planData.focus,
          user: trainee,
        });

        const savedTraining = await this.trainingPlanRepo.save(newTraining);

        const trainingExercises = planData.exercises.map((exData, index) =>
          this.trainingExerciseRepo.create({
            trainingPlan: savedTraining,
            exercise: exercises[index],
            name: exData.name,
            sets: exData.sets,
            reps: exData.reps,
            rest: exData.rest,
            instructions: exData.instructions,
          }),
        );

        await this.trainingExerciseRepo.save(trainingExercises);

        savedTraining.exercises = trainingExercises;

        return savedTraining;
      }),
    );

    return createdTrainings;
  }
}

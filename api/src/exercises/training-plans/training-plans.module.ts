import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  ExerciseEntity,
  TrainingExerciseEntity,
  TrainingPlanEntity,
  TrainingPlanTemplateEntity,
} from 'src/models/excercise';
import { TrainerAssignmentEntity } from 'src/models/profiles';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { TrainingPlansController } from './training-plans.controller';
import { JwtService } from '@nestjs/jwt';
import { TrainingPlansService } from './training-plans.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TrainingPlanEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
      TrainingPlanEntity,
      TrainingExerciseEntity,
      TrainerAssignmentEntity,
      ExerciseEntity,
      TrainingPlanTemplateEntity,
    ]),
  ],
  controllers: [TrainingPlansController],
  providers: [TrainingPlansService, JwtService],
})
export class TrainingPlansModule {}

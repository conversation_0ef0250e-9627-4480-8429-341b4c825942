import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { RolesGuard } from 'src/security/middleware/rolesGuard.middleware';
import { WorkoutLogsService } from './workout-logs.service';
import { Roles } from 'src/security/middleware/roles.decorator';
import { ROLE_VALUES, UserEntity } from 'src/models/user-entity';
import { WorkoutLogEntity } from 'src/models/excercise';
import { CreateWorkoutLogDto, UpdateWorkoutLogDto } from './dto';

@Controller('workout-logs')
@UseGuards(JwtAuthGuard, RolesGuard)
export class WorkoutLogsController {
  constructor(private readonly workoutLogsService: WorkoutLogsService) {}

  @Get()
  @Roles(ROLE_VALUES.TRAINER)
  async findAll(): Promise<WorkoutLogEntity[]> {
    return this.workoutLogsService.findAll();
  }

  @Get('user/:userId')
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER)
  async findByUser(
    @Param('userId') userId: string,
    @Request() req,
  ): Promise<WorkoutLogEntity[]> {
    const trainee: UserEntity = req.user;
    const roleValue = await this.workoutLogsService.getRoleById(trainee.roleId);
    return this.workoutLogsService.findByUser(userId, req.user, roleValue);
  }

  @Post()
  @Roles(ROLE_VALUES.TRAINEE)
  async create(
    @Body() createDto: CreateWorkoutLogDto,
    @Request() req,
  ): Promise<WorkoutLogEntity> {
    const trainee: UserEntity = req.user;
    const roleValue = await this.workoutLogsService.getRoleById(trainee.roleId);
    const newWorkOutLog = await this.workoutLogsService.create(
      createDto,
      req.user,
      roleValue.value,
    );
    return newWorkOutLog;
  }

  @Put(':id')
  @Roles(ROLE_VALUES.TRAINEE)
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateWorkoutLogDto,
    @Request() req,
  ): Promise<WorkoutLogEntity> {
    const trainee: UserEntity = req.user;
    const roleValue = await this.workoutLogsService.getRoleById(trainee.roleId);

    return this.workoutLogsService.update(
      id,
      updateDto,
      req.user,
      roleValue.value,
    );
  }

  @Put(':id/complete')
  @Roles(ROLE_VALUES.TRAINEE)
  async completeWorkoutLog(
    @Param('id') id: string,
    @Request() req,
  ): Promise<WorkoutLogEntity> {
    const trainee: UserEntity = req.user;
    const roleValue = await this.workoutLogsService.getRoleById(trainee.roleId);

    return this.workoutLogsService.completeWorkoutLog(
      id,
      req.user,
      roleValue.value,
    );
  }

  @Delete(':id')
  @Roles(ROLE_VALUES.TRAINEE)
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    const trainee: UserEntity = req.user;
    const roleValue = await this.workoutLogsService.getRoleById(trainee.roleId);

    return this.workoutLogsService.remove(id, req.user, roleValue.value);
  }

  @Get('user/:userId/last-week')
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER)
  async findByUserForLastWeek(
    @Param('userId') userId: string,
    @Request() req,
  ): Promise<WorkoutLogEntity[]> {
    const user: UserEntity = req.user;
    const roleValue = await this.workoutLogsService.getRoleById(user.roleId);
    return this.workoutLogsService.findByUserFirstWeek(
      userId,
      user,
      roleValue.value,
    );
  }
}

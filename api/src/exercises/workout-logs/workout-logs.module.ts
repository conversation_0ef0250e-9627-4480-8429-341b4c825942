import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  TrainingPlanEntity,
  WorkoutExerciseEntity,
  WorkoutLogEntity,
} from 'src/models/excercise';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { WorkoutLogsService } from './workout-logs.service';
import { JwtService } from '@nestjs/jwt';
import { WorkoutLogsController } from './workout-logs.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WorkoutLogEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
      TrainingPlanEntity,
      WorkoutExerciseEntity,
    ]),
  ],
  providers: [WorkoutLogsService, JwtService],
  controllers: [WorkoutLogsController],
})
export class WorkoutLogsModule {}

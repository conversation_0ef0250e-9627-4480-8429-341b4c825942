import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  TrainingPlanEntity,
  WorkoutExerciseEntity,
  WorkoutLogEntity,
} from 'src/models/excercise';
import { ROLE_VALUES, RoleEntity, UserEntity } from 'src/models/user-entity';
import { Between, FindOptionsWhere, Repository } from 'typeorm';
import { CreateWorkoutLogDto, UpdateWorkoutLogDto } from './dto';

@Injectable()
export class WorkoutLogsService {
  constructor(
    @InjectRepository(WorkoutLogEntity)
    private readonly workoutLogRepo: Repository<WorkoutLogEntity>,

    @InjectRepository(RoleEntity)
    private roleRepository: Repository<RoleEntity>,

    @InjectRepository(TrainingPlanEntity)
    private trainingPlanRepo: Repository<TrainingPlanEntity>,

    @InjectRepository(WorkoutExerciseEntity)
    private workoutExerciseRepo: Repository<WorkoutExerciseEntity>,

    @InjectRepository(UserEntity)
    private userRepo: Repository<UserEntity>,
  ) {}

  /**
   * Get all workout logs (For Admins/Trainers)
   */
  async findAll(): Promise<WorkoutLogEntity[]> {
    return this.workoutLogRepo.find({ relations: ['sets'] });
  }

  /**
   * Get all workout logs for a specific user
   */

  async findByUser(
    userId: string,
    requestingUser: UserEntity,
    roleValue,
  ): Promise<WorkoutLogEntity[]> {
    // Trainees can only view their own logs
    if (roleValue === ROLE_VALUES.TRAINEE && userId !== requestingUser.id) {
      throw new ForbiddenException(
        'Trainees can only view their own workout logs.',
      );
    }

    // Fetch workout logs with exercises, sets, and exercise details
    const workoutLogs = await this.workoutLogRepo.find({
      where: { user: { id: userId } } as FindOptionsWhere<WorkoutLogEntity>,
      relations: {
        exercises: {
          sets: true,
          exercise: true,
        },
        trainingPlan: true,
      },
    });

    // Transform the data to include exercise category
    return workoutLogs.map((log) => ({
      ...log,
      exercises: log.exercises.map((workoutExercise) => ({
        ...workoutExercise,
        category: workoutExercise.exercise?.category || null,
      })),
    }));
  }

  /**
   * Get a single workout log by ID
   */

  async findOne(
    id: string,
    requestingUser: UserEntity,
    roleValue,
  ): Promise<WorkoutLogEntity> {
    const log = await this.workoutLogRepo.findOne({
      where: { id },
      relations: ['exercises', 'exercises.sets', 'user'],
    });

    if (!log) throw new NotFoundException('Workout log not found');

    // Trainees can only access their own logs
    if (
      roleValue === ROLE_VALUES.TRAINEE &&
      requestingUser.id !== log.user.id
    ) {
      throw new ForbiddenException('Access denied.');
    }

    return log;
  }

  async create(
    dto: CreateWorkoutLogDto,
    trainee: UserEntity,
    roleValue,
  ): Promise<WorkoutLogEntity> {
    if (roleValue !== ROLE_VALUES.TRAINEE) {
      throw new ForbiddenException('Only trainees can log workouts.');
    }

    // Check if a workout log already exists for the same user, date, and training plan
    const existingLog = await this.workoutLogRepo.findOne({
      where: {
        user: { id: trainee.id },
        date: dto.date,
        trainingPlan: { id: dto.trainingPlanId },
      },
      relations: ['exercises'],
    });

    if (existingLog) {
      throw new ForbiddenException("You've already completed a workout today.");
    }

    const trainingPlan = await this.trainingPlanRepo.findOne({
      where: { id: dto.trainingPlanId, user: { id: trainee.id } },
      relations: ['exercises', 'exercises.exercise'],
    });

    if (!trainingPlan) {
      throw new ForbiddenException(
        'Trainee does not have access to this training plan.',
      );
    }

    // Ensure exercises exist in the training plan
    const trainingExercises = trainingPlan.exercises ?? [];

    const workoutLog = this.workoutLogRepo.create({
      trainingPlan: trainingPlan,
      user: trainee,
      date: dto.date,
      duration: dto.duration || null,
      notes: dto.notes || null,
    });

    const savedWorkoutLog = await this.workoutLogRepo.save(workoutLog);

    // Create and associate workout exercises correctly
    const workoutExercises = await Promise.all(
      trainingExercises.map(async (trainingExercise) => {
        return this.workoutExerciseRepo.create({
          workoutLog: savedWorkoutLog,
          exercise: trainingExercise.exercise,
          name: trainingExercise.name,
        });
      }),
    );

    const savedExercises =
      await this.workoutExerciseRepo.save(workoutExercises);

    // Assign the saved exercises to workoutLog
    savedWorkoutLog.exercises = savedExercises;

    // Update the firstWorkoutDate if not set
    if (!trainee.firstWorkoutDate) {
      trainee.firstWorkoutDate = dto.date;
      await this.userRepo.save(trainee);
    }

    return savedWorkoutLog;
  }

  /**
   * Update a workout log (Only Trainees)
   */
  async update(
    id: string,
    updateDto: UpdateWorkoutLogDto,
    trainee: UserEntity,
    roleValue,
  ): Promise<WorkoutLogEntity> {
    const log = await this.findOne(id, trainee, roleValue);

    if (roleValue !== ROLE_VALUES.TRAINEE) {
      throw new ForbiddenException(
        'Only trainees can update their workout logs.',
      );
    }

    await this.workoutLogRepo.update(id, updateDto);
    return this.findOne(id, trainee, roleValue);
  }

  /**
   * Delete a workout log (Only Trainees)
   */
  async remove(id: string, trainee: UserEntity, roleValue): Promise<void> {
    const log = await this.findOne(id, trainee, roleValue);

    if (trainee.role.value !== ROLE_VALUES.TRAINEE) {
      throw new ForbiddenException(
        'Only trainees can delete their workout logs.',
      );
    }

    const result = await this.workoutLogRepo.delete(id);
    if (result.affected === 0)
      throw new NotFoundException('Workout log not found');
  }

  async completeWorkoutLog(
    id: string,
    trainee: UserEntity,
    roleValue,
  ): Promise<WorkoutLogEntity> {
    const log = await this.findOne(id, trainee, roleValue);

    if (roleValue !== ROLE_VALUES.TRAINEE) {
      throw new ForbiddenException(
        'Only trainees can complete their workout logs.',
      );
    }

    log.duration = new Date().toISOString();
    await this.workoutLogRepo.save(log);

    return log;
  }

  async findByUserFirstWeek(
    userId: string,
    requestingUser: UserEntity,
    roleValue: string,
  ): Promise<any> {
    if (roleValue === ROLE_VALUES.TRAINEE && userId !== requestingUser.id) {
      throw new ForbiddenException(
        'Trainees can only view their own workout logs.',
      );
    }

    // Get the user's first workout date
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user || !user.firstWorkoutDate) {
      return [];
    }

    // Determine the start and end of the first week
    const firstWeekStart = new Date(user.firstWorkoutDate);
    const firstWeekEnd = new Date(firstWeekStart);
    firstWeekEnd.setDate(firstWeekStart.getDate() + 6);

    const formattedStartDate = firstWeekStart.toISOString().split('T')[0];
    const formattedEndDate = firstWeekEnd.toISOString().split('T')[0];

    // Fetch only logs within that first week
    const workoutLogs = await this.workoutLogRepo.find({
      where: {
        user: { id: userId },
        date: Between(formattedStartDate, formattedEndDate),
      } as FindOptionsWhere<WorkoutLogEntity>,
      relations: ['exercises', 'exercises.sets', 'trainingPlan'],
      order: { date: 'ASC' },
    });

    // Map logs to training plans
    const trainingPlanMap = new Map<string, any>();

    workoutLogs.forEach((log) => {
      if (!log.trainingPlan) return;

      const trainingPlanKey = `${log.trainingPlan.id}-${log.date}`;

      if (!trainingPlanMap.has(trainingPlanKey)) {
        trainingPlanMap.set(trainingPlanKey, {
          trainingPlan: {
            id: log.trainingPlan.id,
            day: log.trainingPlan.day,
            focus: log.trainingPlan.focus,
            date: log.date,
            duration: log.duration,
            exercises: [],
          },
        });
      }

      const trainingPlan = trainingPlanMap.get(trainingPlanKey);

      log.exercises.forEach((exercise) => {
        trainingPlan.trainingPlan.exercises.push({
          id: exercise.id,
          name: exercise.name,
          sets: exercise.sets.map((set) => ({
            id: set.id,
            setNumber: set.setNumber,
            weight: set.weight,
            reps: set.reps,
          })),
        });
      });
    });

    return Array.from(trainingPlanMap.values());
  }

  async getRoleById(roleId: number): Promise<RoleEntity> {
    return this.roleRepository.findOne({ where: { id: roleId } });
  }
}

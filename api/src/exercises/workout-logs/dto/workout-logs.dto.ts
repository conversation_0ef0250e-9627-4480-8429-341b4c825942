import { IsNotEmpty, IsString, IsOptional, IsUUID } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateWorkoutLogDto {
  @ApiProperty({
    example: 'b2a3f68e-7c88-44a9-bbc6-123456789abc',
    description: 'Training Plan ID from which the workout log is created',
  })
  @IsUUID()
  @IsNotEmpty()
  trainingPlanId: string;

  @ApiProperty({
    example: '2024-03-21',
    description: 'Date of the workout in YYYY-MM-DD format',
  })
  @IsNotEmpty()
  @IsString()
  date: string;

  @ApiPropertyOptional({
    example: '90 minutes',
    description: 'Total duration of the workout (optional)',
  })
  @IsOptional()
  @IsString()
  duration?: string;

  @ApiPropertyOptional({
    example: 'Felt strong today, increased bench press weight.',
    description: 'Additional notes about the workout (optional)',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateWorkoutLogDto extends PartialType(CreateWorkoutLogDto) {}

import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  ExerciseEntity,
  WorkoutExerciseEntity,
  WorkoutLogEntity,
} from 'src/models/excercise';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { WorkoutExercisesController } from './workout-exercise.controller';
import { JwtService } from '@nestjs/jwt';
import { WorkoutExercisesService } from './workout-exercise.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WorkoutExerciseEntity,
      WorkoutLogEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
      ExerciseEntity,
    ]),
  ],
  controllers: [WorkoutExercisesController],
  providers: [WorkoutExercisesService, JwtService],
})
export class WorkoutExerciseModule {}

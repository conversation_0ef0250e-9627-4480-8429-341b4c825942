import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  ExerciseEntity,
  WorkoutExerciseEntity,
  WorkoutLogEntity,
} from 'src/models/excercise';
import { RoleEntity, UserEntity } from 'src/models/user-entity';
import { Repository } from 'typeorm';
import { CreateWorkoutExerciseDto, UpdateWorkoutExerciseDto } from './dto';

@Injectable()
export class WorkoutExercisesService {
  constructor(
    @InjectRepository(WorkoutExerciseEntity)
    private readonly workoutExerciseRepo: Repository<WorkoutExerciseEntity>,

    @InjectRepository(WorkoutLogEntity)
    private readonly workoutLogRepo: Repository<WorkoutLogEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepository: Repository<RoleEntity>,

    @InjectRepository(ExerciseEntity)
    private readonly exerciseRepo: Repository<ExerciseEntity>,
  ) {}

  /**
   * Get all workout exercises for a specific workout log
   */
  async findByWorkoutLog(
    workoutLogId: string,
  ): Promise<WorkoutExerciseEntity[]> {
    return this.workoutExerciseRepo.find({
      where: { workoutLog: { id: workoutLogId } },
      relations: ['workoutLog'],
    });
  }

  /**
   * Get a single workout exercise by ID
   */
  async findOne(id: string): Promise<WorkoutExerciseEntity> {
    const exercise = await this.workoutExerciseRepo.findOne({
      where: { id },
      relations: ['workoutLog'],
    });

    if (!exercise) throw new NotFoundException('Workout exercise not found');

    return exercise;
  }

  /**
   * Create a new workout exercise (Only Trainees)
   */
  async create(
    dto: CreateWorkoutExerciseDto,
    trainee: UserEntity,
  ): Promise<WorkoutExerciseEntity> {
    const workoutLog = await this.workoutLogRepo.findOne({
      where: { id: dto.workoutLogId },
      relations: ['user'],
    });

    if (!workoutLog) {
      throw new NotFoundException('Workout log not found');
    }

    if (workoutLog.user.id !== trainee.id) {
      throw new ForbiddenException('Unauthorized to modify this workout log');
    }

    const exercise = await this.exerciseRepo.findOne({
      where: { id: dto.exerciseId },
    });
    if (!exercise) {
      throw new NotFoundException('Exercise not found');
    }

    const workoutExercise = this.workoutExerciseRepo.create({
      workoutLog,
      exercise,
      name: dto.name,
    });

    return this.workoutExerciseRepo.save(workoutExercise);
  }

  /**
   * Update a workout exercise (Only Trainees)
   */

  async update(
    id: string,
    updateDto: UpdateWorkoutExerciseDto,
    trainee: UserEntity,
  ): Promise<WorkoutExerciseEntity> {
    const exercise = await this.findOne(id);

    if (exercise.workoutLog.user.id !== trainee.id) {
      throw new ForbiddenException(
        'You do not have permission to update this exercise.',
      );
    }

    await this.workoutExerciseRepo.update(id, updateDto);
    return this.findOne(id);
  }

  /**
   * Delete a workout exercise (Only Trainees)
   */

  async remove(id: string, trainee: UserEntity): Promise<void> {
    const exercise = await this.findOne(id);

    if (exercise.workoutLog.user.id !== trainee.id) {
      throw new ForbiddenException(
        'You do not have permission to delete this exercise.',
      );
    }

    const result = await this.workoutExerciseRepo.delete(id);
    if (result.affected === 0)
      throw new NotFoundException('Workout exercise not found');
  }

  async getRoleById(roleId: number): Promise<RoleEntity> {
    return this.roleRepository.findOne({ where: { id: roleId } });
  }
}

import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { RolesGuard } from 'src/security/middleware/rolesGuard.middleware';
import { WorkoutExercisesService } from './workout-exercise.service';
import { ROLE_VALUES, UserEntity } from 'src/models/user-entity';
import { Roles } from 'src/security/middleware/roles.decorator';
import { WorkoutExerciseEntity } from 'src/models/excercise';
import { CreateWorkoutExerciseDto, UpdateWorkoutExerciseDto } from './dto';

@ApiTags('Workout Exercises')
@Controller('workout-exercises')
@UseGuards(JwtAuthGuard, RolesGuard)
export class WorkoutExercisesController {
  constructor(
    private readonly workoutExercisesService: WorkoutExercisesService,
  ) {}

  @Get('workout-log/:workoutLogId')
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER)
  @ApiOperation({
    summary: 'Get all workout exercises for a specific workout log',
  })
  @ApiParam({
    name: 'workoutLogId',
    required: true,
    description: 'ID of the workout log',
  })
  async findByWorkoutLog(
    @Param('workoutLogId') workoutLogId: string,
  ): Promise<WorkoutExerciseEntity[]> {
    return this.workoutExercisesService.findByWorkoutLog(workoutLogId);
  }

  @Post()
  @Roles(ROLE_VALUES.TRAINEE)
  @ApiOperation({ summary: 'Log a new workout exercise (Only Trainees)' })
  async create(
    @Body() createDto: CreateWorkoutExerciseDto,
    @Request() req,
  ): Promise<WorkoutExerciseEntity> {
    const user: UserEntity = req.user;
    const roleValue = await this.workoutExercisesService.getRoleById(
      user.roleId,
    );
    return this.workoutExercisesService.create(createDto, req.user);
  }

  @Put(':id')
  @Roles(ROLE_VALUES.TRAINEE)
  @ApiOperation({ summary: 'Update a workout exercise (Only Trainees)' })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'ID of the workout exercise',
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateWorkoutExerciseDto,
    @Request() req,
  ): Promise<WorkoutExerciseEntity> {
    return this.workoutExercisesService.update(id, updateDto, req.user);
  }

  @Delete(':id')
  @Roles(ROLE_VALUES.TRAINEE)
  @ApiOperation({ summary: 'Delete a workout exercise (Only Trainees)' })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'ID of the workout exercise',
  })
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.workoutExercisesService.remove(id, req.user);
  }
}

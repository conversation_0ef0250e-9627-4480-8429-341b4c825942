import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Param,
  Post,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { RolesGuard } from 'src/security/middleware/rolesGuard.middleware';
import { TrainingExercisesService } from './training-exercises.service';
import { ROLE_VALUES, UserEntity } from 'src/models/user-entity';
import { Roles } from 'src/security/middleware/roles.decorator';
import { TrainingExerciseEntity } from 'src/models/excercise';
import { CreateTrainingExerciseDto, UpdateTrainingExerciseDto } from './dto';

@ApiTags('Training Exercises')
@Controller('training-exercises')
@UseGuards(JwtAuthGuard, RolesGuard)
export class TrainingExercisesController {
  constructor(
    private readonly trainingExercisesService: TrainingExercisesService,
  ) {}

  @Get()
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER)
  @ApiOperation({ summary: 'Get all training exercises' })
  async findAll(): Promise<TrainingExerciseEntity[]> {
    return this.trainingExercisesService.findAll();
  }

  @Get('plan/:trainingPlanId')
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER)
  @ApiOperation({ summary: 'Get exercises for a specific training plan' })
  @ApiParam({
    name: 'trainingPlanId',
    example: 'b2a3f68e-7c88-44a9-bbc6-123456789abc',
    description: 'Training plan ID',
  })
  async findByTrainingPlan(
    @Param('trainingPlanId') trainingPlanId: string,
  ): Promise<TrainingExerciseEntity[]> {
    return this.trainingExercisesService.findByTrainingPlan(trainingPlanId);
  }

  @Post()
  @Roles(ROLE_VALUES.TRAINER)
  @ApiOperation({
    summary: 'Add an exercise to a training plan (Trainer only)',
  })
  @ApiBody({ type: CreateTrainingExerciseDto })
  async create(
    @Body() createDto: CreateTrainingExerciseDto,
    @Request() req,
  ): Promise<TrainingExerciseEntity> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.trainingExercisesService.getRoleById(
      trainer.roleId,
    );

    if (roleValue.value !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException(
        'Only trainers can add exercises to training plans.',
      );
    }

    return this.trainingExercisesService.create(
      createDto,
      trainer,
      roleValue.value,
    );
  }

  @Put(':id')
  @Roles(ROLE_VALUES.TRAINER)
  @ApiOperation({
    summary: 'Update an exercise in a training plan (Trainer only)',
  })
  @ApiParam({
    name: 'id',
    example: '3c9f8abc-1234-5678-bcde-9876543210ab',
    description: 'Training exercise ID',
  })
  @ApiBody({ type: UpdateTrainingExerciseDto })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateTrainingExerciseDto,
    @Request() req,
  ): Promise<TrainingExerciseEntity> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.trainingExercisesService.getRoleById(
      trainer.roleId,
    );

    if (roleValue.value !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException(
        'Only trainers can update training exercises.',
      );
    }

    return this.trainingExercisesService.update(
      id,
      updateDto,
      trainer,
      roleValue.value,
    );
  }

  @Delete(':id')
  @Roles(ROLE_VALUES.TRAINER)
  @ApiOperation({
    summary: 'Remove an exercise from a training plan (Trainer only)',
  })
  @ApiParam({
    name: 'id',
    example: '3c9f8abc-1234-5678-bcde-9876543210ab',
    description: 'Training exercise ID',
  })
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.trainingExercisesService.getRoleById(
      trainer.roleId,
    );

    if (roleValue.value !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException(
        'Only trainers can delete training exercises.',
      );
    }

    return this.trainingExercisesService.remove(id, trainer, roleValue.value);
  }
}

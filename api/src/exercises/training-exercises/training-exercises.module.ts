import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainingExerciseEntity } from 'src/models/excercise';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { TrainingExercisesService } from './training-exercises.service';
import { JwtService } from '@nestjs/jwt';
import { TrainingExercisesController } from './training-exercises.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TrainingExerciseEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
    ]),
  ],
  providers: [TrainingExercisesService, JwtService],
  controllers: [TrainingExercisesController],
})
export class TrainingExercisesModule {}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExerciseEntity } from 'src/models/excercise';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { TrainingExercisesModule } from './training-exercises/training-exercises.module';
import { TrainingPlansModule } from './training-plans/training-plans.module';
import { WorkoutExerciseModule } from './workout-exercise/workout-exercise.module';
import { WorkoutLogsModule } from './workout-logs/workout-logs.module';
import { WorkoutSetsModule } from './workout-sets/workout-sets.module';
import { TrainingPlanTemplatesModule } from './training-plan-templates/training-plan-templates.module';
import { ExercisesController } from './exercises.controller';
import { JwtService } from '@nestjs/jwt';
import { ExercisesService } from './exercises.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ExerciseEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
    ]),
    TrainingExercisesModule,
    TrainingPlansModule,
    WorkoutExerciseModule,
    WorkoutLogsModule,
    WorkoutSetsModule,
    TrainingPlanTemplatesModule,
  ],
  controllers: [ExercisesController],
  providers: [ExercisesService, JwtService],
})
export class ExercisesModule {}

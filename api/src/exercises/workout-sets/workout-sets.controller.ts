import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Request,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { RolesGuard } from 'src/security/middleware/rolesGuard.middleware';
import { WorkoutSetsService } from './workout-sets.service';
import { ROLE_VALUES, UserEntity } from 'src/models/user-entity';
import { Roles } from 'src/security/middleware/roles.decorator';
import { WorkoutSetEntity } from 'src/models/excercise';
import {
  CreateWorkoutSetDto,
  UpdateWorkoutSetDto,
} from './dto/workout-sets.dto';

@ApiTags('Workout Sets')
@Controller('workout-sets')
@UseGuards(JwtAuthGuard, RolesGuard)
export class WorkoutSetsController {
  constructor(private readonly workoutSetsService: WorkoutSetsService) {}

  @Get()
  @Roles(ROLE_VALUES.TRAINER)
  @ApiOperation({ summary: 'Get all workout sets (For Trainers/Admins)' })
  async findAll(): Promise<WorkoutSetEntity[]> {
    return this.workoutSetsService.findAll();
  }

  @Get('workout-exercise/:workoutExerciseId')
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER)
  @ApiOperation({
    summary: 'Get all workout sets for a specific workout exercise',
  })
  @ApiParam({
    name: 'workoutExerciseId',
    required: true,
    description: 'ID of the workout exercise',
  })
  async findByWorkoutExercise(
    @Param('workoutExerciseId') workoutExerciseId: string,
  ): Promise<WorkoutSetEntity[]> {
    return this.workoutSetsService.findByWorkoutExercise(workoutExerciseId);
  }

  @Post()
  @Roles(ROLE_VALUES.TRAINEE)
  @ApiOperation({ summary: 'Log a new workout set (Only Trainees)' })
  async create(
    @Body() createDto: CreateWorkoutSetDto,
    @Request() req,
  ): Promise<WorkoutSetEntity> {
    const user: UserEntity = req.user;
    const roleValue = await this.workoutSetsService.getRoleById(user.roleId);

    return this.workoutSetsService.create(createDto, req.user, roleValue.value);
  }

  @Put(':id')
  @Roles(ROLE_VALUES.TRAINEE)
  @ApiOperation({ summary: 'Update a workout set (Only Trainees)' })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'ID of the workout set',
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateWorkoutSetDto,
    @Request() req,
  ): Promise<WorkoutSetEntity> {
    return this.workoutSetsService.update(id, updateDto, req.user);
  }

  @Delete(':id')
  @Roles(ROLE_VALUES.TRAINEE)
  @ApiOperation({ summary: 'Delete a workout set (Only Trainees)' })
  @ApiParam({
    name: 'id',
    required: true,
    description: 'ID of the workout set',
  })
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    return this.workoutSetsService.remove(id, req.user);
  }
}

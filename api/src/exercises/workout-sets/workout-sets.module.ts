import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkoutExerciseEntity, WorkoutSetEntity } from 'src/models/excercise';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { WorkoutSetsService } from './workout-sets.service';
import { JwtService } from '@nestjs/jwt';
import { WorkoutSetsController } from './workout-sets.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WorkoutSetEntity,
      RoleEntity,
      UserEntity,
      WorkoutExerciseEntity,
      AccessTokenEntity,
    ]),
  ],
  providers: [WorkoutSetsService, JwtService],
  controllers: [WorkoutSetsController],
})
export class WorkoutSetsModule {}

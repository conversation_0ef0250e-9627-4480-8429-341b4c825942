import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WorkoutExerciseEntity, WorkoutSetEntity } from 'src/models/excercise';
import { ROLE_VALUES, RoleEntity, UserEntity } from 'src/models/user-entity';
import { Repository } from 'typeorm';
import {
  CreateWorkoutSetDto,
  UpdateWorkoutSetDto,
} from './dto/workout-sets.dto';

@Injectable()
export class WorkoutSetsService {
  constructor(
    @InjectRepository(WorkoutSetEntity)
    private readonly workoutSetRepo: Repository<WorkoutSetEntity>,

    @InjectRepository(WorkoutExerciseEntity)
    private readonly workoutExerciseRepo: Repository<WorkoutExerciseEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepository: Repository<RoleEntity>,
  ) {}

  /**
   * Get all workout sets (For Admins/Trainers)
   */
  async findAll(): Promise<WorkoutSetEntity[]> {
    return this.workoutSetRepo.find({ relations: ['workoutExercise'] });
  }

  /**
   * Get all workout sets for a specific workout exercise
   */
  async findByWorkoutExercise(
    workoutExerciseId: string,
  ): Promise<WorkoutSetEntity[]> {
    return this.workoutSetRepo.find({
      where: { workoutExercise: { id: workoutExerciseId } },
      relations: ['workoutExercise'],
    });
  }

  /**
   * Get a single workout set by ID
   */
  async findOne(id: string): Promise<WorkoutSetEntity> {
    const set = await this.workoutSetRepo.findOne({
      where: { id },
      relations: ['workoutExercise'],
    });

    if (!set) throw new NotFoundException('Workout set not found');

    return set;
  }

  /**
   * Create a new workout set (Only Trainees)
   */

  async create(
    dto: CreateWorkoutSetDto,
    trainee: UserEntity,
    roleValue,
  ): Promise<WorkoutSetEntity> {
    if (roleValue !== ROLE_VALUES.TRAINEE) {
      throw new ForbiddenException('Only trainees can log workout sets.');
    }

    const workoutExercise = await this.workoutExerciseRepo.findOne({
      where: { id: dto.workoutExerciseId },
      relations: ['workoutLog', 'workoutLog.user'],
    });

    if (!workoutExercise) {
      throw new NotFoundException('Workout exercise not found');
    }

    const existingSet = await this.workoutSetRepo.findOne({
      where: {
        workoutExercise: { id: dto.workoutExerciseId },
        setNumber: dto.setNumber,
      },
    });

    if (existingSet) {
      throw new ForbiddenException(
        `Set ${dto.setNumber} already exists for this exercise.`,
      );
    }

    const workoutSet = this.workoutSetRepo.create({
      setNumber: dto.setNumber,
      weight: dto.weight,
      reps: dto.reps,
      notes: dto.notes || null,
      workoutExercise,
    });

    return this.workoutSetRepo.save(workoutSet);
  }

  /**
   * Update a workout set (Only Trainees)
   */

  async update(
    id: string,
    updateDto: UpdateWorkoutSetDto,
    trainee: UserEntity,
  ): Promise<WorkoutSetEntity> {
    const set = await this.findOne(id);

    if (set.workoutExercise.workoutLog.user.id !== trainee.id) {
      throw new ForbiddenException(
        'You do not have permission to update this set.',
      );
    }

    await this.workoutSetRepo.update(id, updateDto);
    return this.findOne(id);
  }

  /**
   * Delete a workout set (Only Trainees)
   */

  async remove(id: string, trainee: UserEntity): Promise<void> {
    const set = await this.findOne(id);

    if (set.workoutExercise.workoutLog.user.id !== trainee.id) {
      throw new ForbiddenException(
        'You do not have permission to delete this set.',
      );
    }

    const result = await this.workoutSetRepo.delete(id);
    if (result.affected === 0)
      throw new NotFoundException('Workout set not found');
  }

  async getRoleById(roleId: number): Promise<RoleEntity> {
    return this.roleRepository.findOne({ where: { id: roleId } });
  }
}

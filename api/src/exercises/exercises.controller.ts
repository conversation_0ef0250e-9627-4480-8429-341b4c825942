import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UseGuards,
  Request,
  ForbiddenException,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { RolesGuard } from 'src/security/middleware/rolesGuard.middleware';
import { ExercisesService } from './exercises.service';
import { ROLE_VALUES, UserEntity } from 'src/models/user-entity';
import { ExerciseEntity } from 'src/models/excercise';
import { Roles } from 'src/security/middleware/roles.decorator';
import { CreateExerciseDto, UpdateExerciseDto } from './dto';

@Controller('exercises')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ExercisesController {
  constructor(private readonly exercisesService: ExercisesService) {}

  @Get()
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER)
  async findAll(): Promise<ExerciseEntity[]> {
    return this.exercisesService.findAll();
  }

  @Get('category/:category')
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER)
  async findByCategory(
    @Param('category') category: string,
  ): Promise<ExerciseEntity[]> {
    return this.exercisesService.findByCategory(category);
  }

  @Get(':id')
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER) // Both can view a specific exercise
  async findOne(@Param('id') id: string): Promise<ExerciseEntity> {
    return this.exercisesService.findOne(id);
  }

  @Post()
  @Roles(ROLE_VALUES.TRAINER) // Only trainers can create exercises
  async create(
    @Body() createExerciseDto: CreateExerciseDto,
    @Request() req,
  ): Promise<ExerciseEntity> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.getRoleValue(trainer.roleId.toString());

    if (roleValue !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can create exercises.');
    }

    return this.exercisesService.create(createExerciseDto);
  }

  @Put(':id')
  @Roles(ROLE_VALUES.TRAINER) // Only trainers can update exercises
  async update(
    @Param('id') id: string,
    @Body() updateExerciseDto: UpdateExerciseDto,
    @Request() req,
  ): Promise<ExerciseEntity> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.getRoleValue(trainer.roleId.toString());

    if (roleValue !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can update exercises.');
    }

    return this.exercisesService.update(id, updateExerciseDto);
  }

  @Delete(':id')
  @Roles(ROLE_VALUES.TRAINER) // Only trainers can delete exercises
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.getRoleValue(trainer.roleId.toString());

    if (roleValue !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can delete exercises.');
    }

    return this.exercisesService.remove(id);
  }

  private async getRoleValue(roleId: string): Promise<ROLE_VALUES> {
    // Implement the logic to retrieve the role value based on the role ID
    // This is a placeholder and should be replaced with the actual implementation
    return ROLE_VALUES.TRAINER; // Placeholder return, actual implementation needed
  }
}

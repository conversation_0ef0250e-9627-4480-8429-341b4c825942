import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateExerciseDto {
  @ApiProperty({ example: 'Bench Press', description: 'Name of the exercise' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: 'chest',
    description: 'Category of the exercise (e.g., chest, back, legs, etc.)',
  })
  @IsNotEmpty()
  @IsString()
  category: string;

  @ApiPropertyOptional({
    example: '4 sets',
    description: 'Default number of sets for the exercise',
  })
  @IsOptional()
  @IsString()
  defaultSets?: string;

  @ApiPropertyOptional({
    example: '8-12 reps',
    description: 'Default rep range for the exercise',
  })
  @IsOptional()
  @IsString()
  defaultReps?: string;

  @ApiPropertyOptional({
    example: '90 seconds',
    description: 'Default rest time between sets',
  })
  @IsOptional()
  @IsString()
  defaultRest?: string;

  @ApiPropertyOptional({
    example: 'Slow controlled movement',
    description: 'Instructions on how to perform the exercise',
  })
  @IsOptional()
  @IsString()
  instructions?: string;
}

export class UpdateExerciseDto extends PartialType(CreateExerciseDto) {}

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ExerciseEntity } from 'src/models/excercise';
import { RoleEntity } from 'src/models/user-entity';
import { Repository } from 'typeorm';
import { CreateExerciseDto, UpdateExerciseDto } from './dto';

@Injectable()
export class ExercisesService {
  constructor(
    @InjectRepository(ExerciseEntity)
    private readonly exerciseRepo: Repository<ExerciseEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepository: Repository<RoleEntity>,
  ) {}

  async findAll(): Promise<ExerciseEntity[]> {
    return this.exerciseRepo.find();
  }

  async findByCategory(category: string): Promise<ExerciseEntity[]> {
    return this.exerciseRepo.find({ where: { category } });
  }

  async findOne(id: string): Promise<ExerciseEntity> {
    const exercise = await this.exerciseRepo.findOne({ where: { id } });
    if (!exercise) throw new NotFoundException('Exercise not found');
    return exercise;
  }

  async create(createExerciseDto: CreateExerciseDto): Promise<ExerciseEntity> {
    const exercise = this.exerciseRepo.create(createExerciseDto);
    return this.exerciseRepo.save(exercise);
  }

  async update(
    id: string,
    updateExerciseDto: UpdateExerciseDto,
  ): Promise<ExerciseEntity> {
    await this.findOne(id);
    await this.exerciseRepo.update(id, updateExerciseDto);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const result = await this.exerciseRepo.delete(id);
    if (result.affected === 0)
      throw new NotFoundException('Exercise not found');
  }

  async getRoleById(roleId: number): Promise<RoleEntity> {
    return this.roleRepository.findOne({ where: { id: roleId } });
  }
}
